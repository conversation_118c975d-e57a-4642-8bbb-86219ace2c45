<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<h4 class="py-3 mb-4">
    <span class="text-muted fw-light">Bimbingan Konseling / Pengaturan Point / </span> Tambah Point Permasalahan
</h4>

<div class="row">
    <div class="col-md-12">
        <form id="frmAddSettingspoint" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <div class="card mb-3">
                <h5 class="card-header">Formulir Tambah Point Permasalahan</h5>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="name" class="form-label"><PERSON>a <PERSON> <span class="text-danger">*</span></label>
                                <input type="text" id="name" name="name" placeholder="Masukkan Nama Permasalahan" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="point" class="form-label">Point <span class="text-danger">*</span></label>
                                <input type="number" id="point" name="point" placeholder="Masukkan Point" class="form-control" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end mt-4">
                <a href="<?= base_url('counseling/settingspoint?tab=tab-problem') ?>" class="btn btn-danger">
                    <i class="ti ti-arrow-back me-2"></i>
                    Kembali
                </a>

                <button type="submit" class="btn btn-primary ms-2">
                    <i class="ti ti-check me-2"></i>
                    Simpan
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmAddSettingspoint', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('button[type="submit"]').removeAttr('disabled');
                    return swalMessageSuccess(response.MESSAGE, (ok) => {
                        return window.location.href = '<?= base_url('counseling/settingspoint?tab=tab-problem') ?>';
                    });
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    };
</script>