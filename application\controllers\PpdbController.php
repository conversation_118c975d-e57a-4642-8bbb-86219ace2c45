<?php

defined('BASEPATH') or die('No direct script access allowed!');
/**
 * @property TbPpdb $tbppdb
 * @property MsPpdbpath $msppdbpath
 * @property CI_DB_query_builder $db
 * @property CI_Upload $upload
 * @property Datatables $datatables
 */

class PpdbController extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->model('TbPpdb', 'tbppdb');
        $this->load->model('MsPpdbpath', 'msppdbpath');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['title'] = 'PPDB - Pendaftaran Peserta Didik';
        $data['content'] = 'admin/ppdb/register/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('TbPpdb', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $detail[] = $value->nisn;
            $detail[] = $value->name;
            $detail[] = $value->path_name;
            $detail[] = $value->gender == 'L' ? 'Laki-laki' : 'Perempuan';
            $detail[] = $value->religion;
            $detail[] = $value->birth_place;
            $detail[] = tgl_indo($value->birth_date);
            $detail[] = $value->address;
            $detail[] = $value->origin;
            $detail[] = $value->parentname;
            $detail[] = $value->parentjob;
            $detail[] = $value->parentearnings;
            $detail[] = '<a href="' . base_url('uploads/certificate/' . $value->certificate) . '" download>' . basename($value->certificate) . '</a>';
            $detail[] = $value->average;
            $detail[] = '<img src="' . base_url('uploads/studpic/' . $value->picture) . '" style="width: 100px; height: auto;">';
            $actions = "";
            if (isAdmin()) {
                $actions .= "<div class=\"d-flex\">
                    <a href=\"" . base_url('ppdb/register/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2 mb-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                                                <i class=\"ti ti-edit\"></i>
                                            </a>

                                            <button type=\"button\" class=\"btn btn-danger btn-sm ms-2 mb-2\" onclick=\"deleteStudent('" . $value->id . "', '" . $value->name . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                                                <i class=\"ti ti-trash\"></i>
                                            </button>
                                            </div>";
            }

            $actionswa = "<a href=\"https://wa.me/" . $value->phonenumber . "\" class=\"btn btn-success btn-sm ms-2\" target=\"_blank\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-success\" data-bs-original-title=\"Whatsapp\">
                                            <i class=\"ti ti-brand-whatsapp\"></i>
                                        </a>";

            $actions .= "<div class=\"d-flex\">" . $actionswa . "</div>";

            $detail[] = $actions;

            $data[] = $detail;
        }
        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['title'] = 'PPDB - Tambah Peserta Didik';
        $data['content'] = 'admin/ppdb/register/add';
        $data['jalur'] = $this->msppdbpath->order_by('name', 'ASC')->get(array('createdby' => getCurrentIdUser()))->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $name = getPost('name');
            $nisn = getPost('nisn');
            $phonenumber = getPost('nomor');
            $path = getPost('jalur');
            $gender = getPost('gender');
            $birth_place = getPost('tempat');
            $birth_date = getPost('tgllahir');
            $religion = getPost('religion');
            $address = getPost('alamat');
            $origin = getPost('origin');
            $parentname = getPost('parent');
            $parentjob = getPost('parentjob');
            $parentearnings = getPost('parentearnings');
            $certificate = isset($_FILES['certificate']) ? $_FILES['certificate'] : null;
            $average = getPost('average');
            $picture = isset($_FILES['studpicture']) ? $_FILES['studpicture'] : null;

            if ($name == null) {
                throw new Exception('Nama tidak boleh kosong');
            } elseif ($nisn == null) {
                throw new Exception('NISN tidak boleh kosong');
            } elseif ($phonenumber == null) {
                throw new Exception('No Whatsapp tidak boleh kosong');
            } elseif (empty($path)) {
                throw new Exception('Jalur tidak boleh kosong');
            } elseif ($gender != 'L' && $gender != 'P') {
                throw new Exception('Jenis kelamin tidak valid');
            } elseif ($birth_place == null) {
                throw new Exception('Tempat lahir tidak boleh kosong');
            } elseif (empty($birth_date)) {
                throw new Exception('Tanggal lahir tidak boleh kosong');
            } elseif (empty($religion)) {
                throw new Exception('Agama tidak boleh kosong');
            } elseif ($address == null) {
                throw new Exception('Alamat tidak boleh kosong');
            } elseif ($origin == null) {
                throw new Exception('Asal sekolah tidak boleh kosong');
            } elseif ($parentname == null) {
                throw new Exception('Nama orang tua tidak boleh kosong');
            } elseif (empty($parentjob)) {
                throw new Exception('Pekerjaan orang tua tidak boleh kosong');
            } elseif (empty($parentearnings)) {
                throw new Exception('Penghasilan orang tua tidak boleh kosong');
            } elseif (empty($average)) {
                throw new Exception('Rata-rata nilai tidak boleh kosong');
            }

            if (strlen($phonenumber) < 10 || strlen($phonenumber) > 15 || (substr($phonenumber, 0, 2) != '08' && substr($phonenumber, 0, 3) != '628')) {
                throw new Exception('No Whatsapp tidak valid');
            }
            if (substr($phonenumber, 0, 2) == '08') {
                $phonenumber = '62' . ltrim($phonenumber, '0');
            }

            $ceknisn = $this->tbppdb->select('nisn')->where(array(
                'nisn' => $nisn,
                'createdby' => getCurrentIdUser()
            ))->get();

            if ($ceknisn->num_rows() > 0) {
                throw new Exception('NISN sudah digunakan');
            }

            // Proses upload sertifikat
            $certificate_path = null;
            $picture_path = null;

            // Konfigurasi upload library sekali untuk kedua file
            $this->load->library('upload');

            // Proses upload sertifikat
            $certificate_path = null;
            $picture_path = null;
            $this->load->library('upload');

            if (!empty($certificate) && $certificate['size'] > 0) {
                $upload_config = [
                    'upload_path'   => './uploads/certificate/',
                    'allowed_types' => 'pdf|doc|docx|xls|xlsx',
                    'max_size'      => 10200,
                    'encrypt_name'     => true
                ];

                if ($certificate['size'] > (10200 * 1024)) {
                    throw new Exception('Ukuran file melebihi batas maksimal 10MB');
                }

                $this->upload->initialize($upload_config);
                if (!$this->upload->do_upload('certificate')) {
                    throw new Exception('Gagal mengupload file');
                }

                $certificate_path = $this->upload->data()['file_name'];
            }

            if (!empty($picture) && $picture['size'] > 0) {
                $upload_config = [
                    'upload_path'   => './uploads/studpic/',
                    'allowed_types' => 'jpg|jpeg|png|webp',
                    'max_size'      => 5048,
                    'encrypt_name'     => true,
                ];

                $this->upload->initialize($upload_config);
                if (!$this->upload->do_upload('studpicture')) {
                    throw new Exception('Gagal mengupload foto');
                }

                $picture_path = $this->upload->data()['file_name'];
            }

            // Simpan data ke database
            $insert = array();
            $insert['nisn'] = $nisn;
            $insert['infopath'] = $path;
            $insert['name'] = $name;
            $insert['gender'] = $gender;
            $insert['birth_place'] = $birth_place;
            $insert['birth_date'] = $birth_date;
            $insert['religion'] = $religion;
            $insert['address'] = $address;
            $insert['phonenumber'] = $phonenumber;
            $insert['parentname'] = $parentname;
            $insert['parentjob'] = $parentjob;
            $insert['parentearnings'] = $parentearnings;
            $insert['origin'] = $origin;
            $insert['average'] = $average;
            $insert['certificate'] = $certificate_path;
            $insert['picture'] = $picture_path;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            // Proses insert data
            $this->tbppdb->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data peserta didik gagal ditambahkan');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data peserta didik berhasil ditambahkan');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $student = $this->tbppdb->get(array('id' => $id));

        if ($student->num_rows() == 0) {
            redirect('ppdb/register');
        }

        $data = array();
        $data['jalur'] = $this->msppdbpath->order_by('name', 'ASC')->get(array('createdby' => getCurrentIdUser()))->result();
        $data['student'] = $student->row();
        $data['title'] = 'PPDB - Ubah Peserta Didik';
        $data['content'] = 'admin/ppdb/register/edit';

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $cek = $this->tbppdb->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $cek->row();

            $name = getPost('name');
            $nisn = getPost('nisn');
            $phonenumber = getPost('nomor');
            $path = getPost('jalur');
            $gender = getPost('gender');
            $birth_place = getPost('tempat');
            $birth_date = getPost('tgllahir');
            $religion = getPost('religion');
            $address = getPost('alamat');
            $origin = getPost('origin');
            $parentname = getPost('parent');
            $parentjob = getPost('parentjob');
            $parentearnings = getPost('parentearnings');
            $certificate = $_FILES['certificate'];
            $average = getPost('average');
            $picture = $_FILES['studpicture'];

            if (empty(strlen(trim($name)))) {
                throw new Exception('Nama tidak boleh kosong');
            } else if (empty(strlen(trim($nisn)))) {
                throw new Exception('NISN tidak boleh kosong');
            } else if (empty(strlen(trim($phonenumber)))) {
                throw new Exception('No Whatsapp tidak boleh kosong');
            } else if (empty($path)) {
                throw new Exception('Jalur tidak boleh kosong');
            } else if ($gender != 'L' && $gender != 'P') {
                throw new Exception('Jenis kelamin tidak valid');
            } else if (empty(strlen(trim($birth_place)))) {
                throw new Exception('Tempat lahir tidak boleh kosong');
            } else if (empty($birth_date)) {
                throw new Exception('Tanggal lahir tidak boleh kosong');
            } else if (empty($religion)) {
                throw new Exception('Agama tidak boleh kosong');
            } else if (empty(strlen(trim($address)))) {
                throw new Exception('Alamat tidak boleh kosong');
            } else if (empty(strlen(trim($origin)))) {
                throw new Exception('Asal Sekolah tidak boleh kosong');
            } else if (empty(strlen(trim($parentname)))) {
                throw new Exception('Nama Orang Tua tidak boleh kosong');
            } else if (empty($parentjob)) {
                throw new Exception('Pekerjaan Orang Tua tidak boleh kosong');
            } else if (empty($parentearnings)) {
                throw new Exception('Penghasilan Orang Tua tidak boleh kosong');
            } else if (empty($average)) {
                throw new Exception('Rata-rata nilai tidak boleh kosong');
            } else if (strlen($phonenumber) < 10 || strlen($phonenumber) > 15 || (!str_starts_with($phonenumber, '08') && !str_starts_with($phonenumber, '628'))) {
                throw new Exception('No Whatsapp tidak valid');
            }

            if (str_starts_with($phonenumber, '08')) {
                $phonenumber = '62' . ltrim($phonenumber, '0');
            }

            if ($nisn != $row->nisn) {
                $ceknisn = $this->tbppdb->select('nisn')->where(array(
                    'nisn' => $nisn,
                    'createdby' => getCurrentIdUser(),
                    'id !=' => $id
                ))->get();

                if ($ceknisn->num_rows() > 0) {
                    throw new Exception('NISN sudah digunakan');
                }
            }

            // Konfigurasi upload library sekali untuk kedua file
            $this->load->library('upload');

            // Proses upload sertifikat
            $certificate_path = $row->certificate;
            if (!empty($certificate) && $certificate['size'] > 0) {
                if ($certificate['size'] > (10200 * 1024)) {
                    throw new Exception('Ukuran file melebihi batas maksimal 10MB');
                }

                $upload_config = [
                    'upload_path'   => './uploads/certificate/',
                    'allowed_types' => 'pdf|doc|docx|xls|xlsx',
                    'max_size'      => 10200,
                    'encrypt_name'     => true
                ];

                $this->upload->initialize($upload_config);

                if ($this->upload->do_upload('certificate')) {
                    if (!empty($row->certificate) && file_exists('./uploads/certificate/' . $row->certificate)) {
                        unlink('./uploads/certificate/' . $row->certificate);
                    }
                    $certificate_path = $this->upload->data('file_name');
                } else {
                    throw new Exception('Gagal mengupload file sertifikat');
                }
            }

            $picture_path = $row->picture;
            if (!empty($picture) && $picture['size'] > 0) {
                $upload_config = [
                    'upload_path'   => './uploads/studpic/',
                    'allowed_types' => 'jpg|jpeg|png|webp',
                    'max_size'      => 5048,
                    'encrypt_name'     => true
                ];

                $this->upload->initialize($upload_config);

                if ($this->upload->do_upload('studpicture')) {
                    if (!empty($row->picture) && file_exists('./uploads/studpic/' . $row->picture)) {
                        unlink('./uploads/studpic/' . $row->picture);
                    }
                    $picture_path = $this->upload->data('file_name');
                } else {
                    throw new Exception('Gagal mengupload foto');
                }
            }

            $update = array();
            $update['nisn'] = $nisn;
            $update['infopath'] = $path;
            $update['name'] = $name;
            $update['gender'] = $gender;
            $update['birth_place'] = $birth_place;
            $update['birth_date'] = $birth_date;
            $update['religion'] = $religion;
            $update['address'] = $address;
            $update['phonenumber'] = $phonenumber;
            $update['parentname'] = $parentname;
            $update['parentjob'] = $parentjob;
            $update['parentearnings'] = $parentearnings;
            $update['origin'] = $origin;
            $update['average'] = $average;
            $update['certificate'] = $certificate_path;
            $update['picture'] = $picture_path;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->tbppdb->update(array('id' => $id), $update);

            if ($this->db->trans_status() === TRUE) {
                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Data peserta didik berhasil diubah');
            }

            throw new Exception('Data peserta didik gagal diubah');
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_delete()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $id = getPost('id');

        if (empty($id)) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $get = $this->tbppdb->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser(),
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $get->row();
        $certificate = $row->certificate;
        $picture = $row->picture;

        // Hapus file sertifikat jika ada
        if (!empty($certificate)) {
            $certificatePath = './uploads/certificate/' . $certificate;
            if (file_exists($certificatePath)) {
                unlink($certificatePath);
            }
        }

        // Hapus file foto jika ada
        if (!empty($picture)) {
            $picturePath = './uploads/studpic/' . $picture;
            if (file_exists($picturePath)) {
                unlink($picturePath);
            }
        }

        // Hapus data dari database
        $delete = $this->tbppdb->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data peserta didik berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data peserta didik gagal dihapus');
        }
    }
}
