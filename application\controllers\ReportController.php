<?php

use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property CI_DB_query_builder $db
 * @property MsUsers $msusers
 * @property MsClass $msclass
 * @property MsStudent $msstudent
 * @property MsTeacher $msteacher
 * @property MsPermission $mspermission
 * @property MsScheduleclass $msscheduleclass
 * @property MsScheduleholiday $msscheduleholiday
 * @property MsPermissionteacher $mspermissionteacher
 * @property Attendancecourse $attendancecourse
 * @property Attendancecoursedetail $attendancecoursedetail
 * @property TbAttendance $tbattendance
 * @property TbAttendanceteacher $tbattendanceteacher
 * @property Datatables $datatables
 * @property TbAchievement $tbachievement
 * @property TbProblem $tbproblem
 */
class ReportController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsClass', 'msclass');
        $this->load->model('MsStudent', 'msstudent');
        $this->load->model('MsTeacher', 'msteacher');
        $this->load->model('MsPermission', 'mspermission');
        $this->load->model('MsPermissionteacher', 'mspermissionteacher');
        $this->load->model('MsScheduleclass', 'msscheduleclass');
        $this->load->model('MsScheduleholiday', 'msscheduleholiday');
        $this->load->model('TbAttendance', 'tbattendance');
        $this->load->model('TbAttendanceteacher', 'tbattendanceteacher');
        $this->load->model('TbAttendancecourse', 'attendancecourse');
        $this->load->model('TbAttendancecoursedetail', 'attendancecoursedetail');
        $this->load->model('TbAchievement', 'tbachievement');
        $this->load->model('TbProblem', 'tbproblem');
    }

    public function achievement()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Laporan - Prestasi';
        $data['content'] = 'admin/report/achievement/index';
        $data['class'] = $this->msclass->order_by('roman_to_int(a.level),a.name', 'asc')->get(array(
            'a.createdby' => getCurrentIdUser(),
            'a.isdeleted' => null
        ))->result();

        return $this->load->view('master', $data);
    }

    public function problem()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Laporan - Permasalahan';
        $data['content'] = 'admin/report/problem/index';
        $data['class'] = $this->msclass->order_by('roman_to_int(a.level),a.name', 'asc')->get(array(
            'a.createdby' => getCurrentIdUser(),
            'a.isdeleted' => null
        ))->result();

        return $this->load->view('master', $data);
    }

    public function attendance_student()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Laporan - Absensi Peserta Didik';
        $data['content'] = 'admin/report/attendance/student/index';
        $data['class'] = $this->msclass->order_by('roman_to_int(a.level),a.name', 'asc')->get(array(
            'a.createdby' => getCurrentIdUser(),
            'a.isdeleted' => null
        ))->result();

        return $this->load->view('master', $data);
    }

    public function attendance_student_preview()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $classid = getPost('classid');
        $month = getPost('month');
        $day_num = date('t', strtotime($month));

        if (empty($classid)) {
            return JSONResponseDefault('FAILED', 'Kelas tidak boleh kosong');
        }

        $data = array();
        $data['student'] = $this->msstudent->get(array(
            'classid' => $classid,
            'createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool(),
            "(status IS NULL OR status = 'Active') =" => true
        ))->result();
        $data['month'] = $month;
        $data['amountdate'] = $day_num;
        $data['holiday'] = getSettingsValue('holiday') ?? '';

        $studentid = array_column($data['student'], 'id');

        // Ambil semua data izin untuk semua peserta didik di kelas pada bulan tersebut
        $permissions = $this->db->select('id, studentid, typepermission, startdate, enddate')
            ->from('mspermission')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('startdate <=', date('Y-m-t', strtotime($month))) // akhir bulan
            ->where('enddate >=', date('Y-m-01', strtotime($month))); // awal bulan

        if (count($studentid)) {
            $this->db->where_in('studentid', $studentid);
        }

        $permissions = $this->db->get()->result();

        // Ambil semua data absensi untuk semua peserta didik di kelas pada bulan tersebut
        $attendance = $this->db->select('studentid, status, DATE(date) as date')
            ->from('attendance')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('DATE(date) >=', date('Y-m-01', strtotime($month))) // awal bulan
            ->where('DATE(date) <=', date('Y-m-t', strtotime($month))); // akhir bulan

        if (count($studentid) > 0) {
            $this->db->where_in('studentid', $studentid);
        }

        $attendance = $this->db->get()->result();

        // Liburan
        $holidays = $this->db->select('a.id, b.classid, a.startdate, a.enddate')
            ->from('msscheduleholiday a')
            ->join('msscheduleholidaydetail b', 'a.id = b.scheduleholidayid')
            ->where('a.createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool());

        $classid = array_column($data['student'], 'classid');

        if (count($classid) > 0) {
            $holidays->where_in('b.classid', $classid);
        }

        $holidays = $this->db->get()->result();

        // Masukkan hasil query ke dalam data
        $data['permissions'] = $permissions;
        $data['attendance'] = $attendance;
        $data['holidays'] = $holidays;

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('admin/report/attendance/student/preview', $data, true)
        ));
    }

    public function attendance_student_pdf()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $classid = getGet('classid');
        $month = getGet('month');
        $amountdate = date('t', strtotime($month));

        $getclass = $this->msclass->get(array('id' => $classid));

        if ($getclass->num_rows() == 0) {
            return redirect(base_url('report/attendance/student'));
        }

        $rowclass = $getclass->row();

        $student = $this->msstudent->get(array(
            'classid' => $classid,
            'createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool(),
            "(status IS NULL OR status = 'Active') =" => true,
            'isdeleted' => null
        ))->result();
        $holiday = getSettingsValue('holiday') ?? [];

        // Ambil semua data izin untuk semua peserta didik di kelas pada bulan tersebut
        $permissions = $this->db->select('id, studentid, typepermission, startdate, enddate')
            ->from('mspermission')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('startdate <=', date('Y-m-t', strtotime($month))) // akhir bulan
            ->where('enddate >=', date('Y-m-01', strtotime($month))) // awal bulan
            ->where_in('studentid', array_column($student, 'id'))
            ->get()->result();

        // Ambil semua data absensi untuk semua peserta didik di kelas pada bulan tersebut
        $attendance = $this->db->select('studentid, status, DATE(date) as date')
            ->from('attendance')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('DATE(date) >=', date('Y-m-01', strtotime($month))) // awal bulan
            ->where('DATE(date) <=', date('Y-m-t', strtotime($month))) // akhir bulan
            ->where_in('studentid', array_column($student, 'id'))
            ->get()->result();

        // Liburan
        $holidays = $this->db->select('a.id, b.classid, a.startdate, a.enddate')
            ->from('msscheduleholiday a')
            ->join('msscheduleholidaydetail b', 'a.id = b.scheduleholidayid')
            ->where('a.createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where_in('b.classid', array_column($student, 'classid'))
            ->get()->result();

        $school = $this->msusers->get(array('id' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->row();

        $html2pdf = new Html2Pdf('L', 'A4', 'en', true, 'UTF-8', array(5, 5, 5, 5));
        $html2pdf->writeHTML($this->load->view('admin/report/attendance/student/pdf', array(
            'student' => $student,
            'permissions' => $permissions,
            'attendance' => $attendance,
            'holidays' => $holidays,
            'holiday' => $holiday,
            'month' => $month,
            'amountdate' => $amountdate,
            'class' => $rowclass,
            'school' => $school
        ), true));

        $html2pdf->output();
    }


    public function process_attendance_student()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        ini_set('max_execution_time', 0);

        $classid = getPost('classid');
        $month = getPost('month');
        $day_num = date('t', strtotime($month));

        $holiday = getSettingsValue('holiday');
        $holiday = explode(',', $holiday ?? '');
        $principal_name = getCurrentUser()->principal ?? null;

        $getclass = $this->msclass->get(array('id' => $classid));

        if ($getclass->num_rows() == 0) {
            return redirect(base_url('report/attendance/student'));
        }

        $rowclass = $getclass->row();

        $classname = ($rowclass->level ?? null) != null ? $rowclass->level . ' - ' . $rowclass->name : $rowclass->name;

        if ($month != null) {
            $day_num = date('t', strtotime($month));
        } else {
            $day_num = date('t');
        }

        $letter = 'D';
        for ($i = 0; $i < $day_num; $i++) {
            if ($day_num - 1 == $i) {
                $last_letter = $letter++;
            } else {
                $letter++;
            }
        }

        $first_cell_amount = $letter;

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $style = [
            'font' => ['bold' => true], // Set font nya jadi bold
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // Set text jadi ditengah secara horizontal (center)
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER // Set text jadi di tengah secara vertical (middle)
            ],
            'borders' => [
                'top' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border top dengan garis tipis
                'right' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],  // Set border right dengan garis tipis
                'bottom' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border bottom dengan garis tipis
                'left' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN] // Set border left dengan garis tipis
            ]
        ];

        // Buat sebuah variabel untuk menampung pengaturan style dari header tabel
        $style_col = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // Set text jadi ditengah secara horizontal (center)
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER // Set text jadi di tengah secara vertical (middle)
            ],
            'borders' => [
                'top' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border top dengan garis tipis
                'right' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],  // Set border right dengan garis tipis
                'bottom' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border bottom dengan garis tipis
                'left' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN] // Set border left dengan garis tipis
            ]
        ];

        // Buat sebuah variabel untuk menampung pengaturan style dari header tabel
        $style_ttd = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // Set text jadi ditengah secara horizontal (center)
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER // Set text jadi di tengah secara vertical (middle)
            ]
        ];

        $sheet->setCellValue('A1', "Laporan Absensi Peserta Didik\nKelas $classname");
        $sheet->mergeCells('A1:AL2');
        $sheet->getStyle('A1')->getAlignment()->setWrapText(true);

        // Set Merge Cell pada kolom A1 sampai E1
        $sheet->getStyle('A1')->applyFromArray($style); // Set bold kolom A1

        // Buat header tabel nya pada baris ke 3
        $sheet->setCellValue('A3', "NO");
        $sheet->setCellValue('B3', "NIS");
        $sheet->setCellValue('C3', "NAMA");
        $sheet->setCellValue('D3', "TANGGAL");
        $sheet->setCellValue($first_cell_amount . '3', "JUMLAH");

        $bkup_cell_amount = $first_cell_amount;
        for ($i = 0; $i <= 3; $i++) {
            $last_cell_amount = $bkup_cell_amount++;
        }

        //Merge header
        $sheet->mergeCells('A3:A5');
        $sheet->mergeCells('B3:B5');
        $sheet->mergeCells('C3:C5');
        $sheet->mergeCells('D3:' . $last_letter . '3');
        $sheet->mergeCells($first_cell_amount . '3:' . $last_cell_amount . '4');

        // Apply style header yang telah kita buat tadi ke masing-masing kolom header
        $sheet->getStyle('A3:A5')->applyFromArray($style_col);
        $sheet->getStyle('B3:B5')->applyFromArray($style_col);
        $sheet->getStyle('C3:C5')->applyFromArray($style_col);
        $sheet->getStyle('D3:' . $last_letter . '3')->applyFromArray($style_col);
        $sheet->getStyle($first_cell_amount . '3:' . $last_cell_amount . '4')->applyFromArray($style_col);

        // Set width kolom
        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(40);
        $sheet->getColumnDimension('C')->setWidth(40);

        $first_coldim = 'D';

        for ($i = 0; $i < $day_num; $i++) {
            $sheet->getColumnDimension($first_coldim)->setWidth(5);
            $first_coldim++;
        }

        $sheet->getColumnDimension($first_coldim++)->setWidth(5);
        $sheet->getColumnDimension($first_coldim++)->setWidth(5);
        $sheet->getColumnDimension($first_coldim++)->setWidth(5);
        $sheet->getColumnDimension($first_coldim++)->setWidth(5);

        $first_cellval = 'D';
        for ($i = 1; $i <= $day_num; $i++) {
            $date = date("$i/m/Y", strtotime($month));
            $sheet->setCellValue($first_cellval . '4', $date);
            $first_cellval++;
        }

        $first_cellstyle = 'D';
        for ($i = 0; $i < $day_num; $i++) {
            $sheet->getStyle($first_cellstyle . '4')->applyFromArray($style_col);
            $sheet->getStyle($first_cellstyle . '4')->getAlignment()->setTextRotation(90);
            $first_cellstyle++;
        }

        $first_cellval = 'D';
        for ($i = 0; $i < $day_num; $i++) {
            $sheet->setCellValue($first_cellval . '5', "1");
            $first_cellval++;
        }

        $sheet->setCellValue($first_cellval++ . '5', "H");
        $sheet->setCellValue($first_cellval++ . '5', "I");
        $sheet->setCellValue($first_cellval++ . '5', "S");
        $sheet->setCellValue($first_cellval++ . '5', "A");

        $first_cellstyle = 'D';
        for ($i = 0; $i < $day_num; $i++) {
            $sheet->getStyle($first_cellstyle . '5')->applyFromArray($style_col);
            $first_cellstyle++;
        }

        $sheet->getStyle($first_cellstyle++ . '5')->applyFromArray($style_col);
        $sheet->getStyle($first_cellstyle++ . '5')->applyFromArray($style_col);
        $sheet->getStyle($first_cellstyle++ . '5')->applyFromArray($style_col);
        $sheet->getStyle($first_cellstyle++ . '5')->applyFromArray($style_col);

        $student = $this->msstudent->get(array(
            'classid' => $classid,
            'createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool(),
            "(status IS NULL OR status = 'Active') =" => true,
            'isdeleted' => null
        ))->result();

        $holiday = getSettingsValue('holiday') ?? [];

        // Ambil semua data izin untuk semua peserta didik di kelas pada bulan tersebut
        $permissions = $this->db->select('id, studentid, typepermission, startdate, enddate')
            ->from('mspermission')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('startdate <=', date('Y-m-t', strtotime($month))) // akhir bulan
            ->where('enddate >=', date('Y-m-01', strtotime($month))) // awal bulan
            ->where_in('studentid', array_column($student, 'id'))
            ->get()->result();

        // Ambil semua data absensi untuk semua peserta didik di kelas pada bulan tersebut
        $attendance = $this->db->select('studentid, status, DATE(date) as date')
            ->from('attendance')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('DATE(date) >=', date('Y-m-01', strtotime($month))) // awal bulan
            ->where('DATE(date) <=', date('Y-m-t', strtotime($month))) // akhir bulan
            ->where_in('studentid', array_column($student, 'id'))
            ->get()->result();

        // Liburan
        $holidays = $this->db->select('a.id, b.classid, a.startdate, a.enddate')
            ->from('msscheduleholiday a')
            ->join('msscheduleholidaydetail b', 'a.id = b.scheduleholidayid')
            ->where('a.createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where_in('b.classid', array_column($student, 'classid'))
            ->get()->result();

        $school = $this->msusers->get(array('id' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->row();


        $no = 1;
        $i = 6;
        $global_cellval_first = "AI";
        $global_cellval_end = "AI";

        $holidaySettings = explode(',', getSettingsValue('holiday') ?? '');

        foreach ($student as $key => $value) {
            $sheet->setCellValue('A' . $i, $no++);
            $sheet->setCellValue('B' . $i, $value->nis);
            $sheet->setCellValue('C' . $i, $value->name);

            $h = $iz = $s = $a = 0;

            $first_cellval_loop = 'D';

            for ($j = 0; $j < $day_num; $j++) {
                $apd_date = ($j + 1);
                $apd_month = date('m', strtotime($month));
                $apd_year = date('Y', strtotime($month));

                $format = "$apd_year-$apd_month-$apd_date";
                $currentDate = date('Y-m-d', strtotime($format));
                $days = hari_ini(date('D', strtotime(date("Y-m-d", strtotime($month . '-' . $apd_date)))));

                // Cek apakah tanggal ini libur
                $isHoliday = in_array($days, $holidaySettings);
                foreach ($holidays as $holiday) {
                    if ($currentDate >= $holiday->startdate && $currentDate <= $holiday->enddate && $holiday->classid == $value->classid) {
                        $isHoliday = true;
                        break;
                    }
                }

                // Cek apakah peserta didik memiliki izin pada tanggal ini
                $hasPermission = false;
                foreach ($permissions as $permission) {
                    if ($currentDate >= $permission->startdate && $currentDate <= $permission->enddate && $permission->studentid == $value->id) {
                        $hasPermission = $permission->typepermission;
                        break;
                    }
                }

                // Cek apakah ada absensi pada tanggal ini
                $attendanceStatus = null;
                foreach ($attendance as $att) {
                    if ($att->studentid == $value->id && $att->date == $currentDate) {
                        $attendanceStatus = $att->status;
                        break;
                    }
                }

                if ($isHoliday) {
                    $sheet->setCellValue($first_cellval_loop . $i, "L");
                } else if ($hasPermission) {
                    if ($hasPermission == 'permission') {
                        $sheet->setCellValue($first_cellval_loop . $i, "I");
                        $iz++;
                    } else {
                        $sheet->setCellValue($first_cellval_loop . $i, "S");
                        $s++;
                    }
                } else if ($attendanceStatus) {
                    if ($attendanceStatus == 'Late' || $attendanceStatus == 'Entry') {
                        $sheet->setCellValue($first_cellval_loop . $i, "H");
                        $h++;
                    }
                } else if (getCurrentDate() > $currentDate) {
                    $sheet->setCellValue($first_cellval_loop . $i, "A");
                    $a++;
                } else {
                    $sheet->setCellValue($first_cellval_loop . $i, "-");
                }

                $first_cellval_loop++;
            }

            $sheet->setCellValue($first_cellval_loop++ . $i, $h == 0 ? '-' : $h);
            $sheet->setCellValue($first_cellval_loop++ . $i, $iz == 0 ? '-' : $iz);
            $sheet->setCellValue($first_cellval_loop++ . $i, $s == 0 ? '-' : $s);
            $global_cellval_end = $first_cellval_loop;
            $sheet->setCellValue($first_cellval_loop++ . $i, $a == 0 ? '-' : $a);

            $sheet->getStyle('A' . $i)->applyFromArray($style_col);
            $sheet->getStyle('B' . $i)->applyFromArray($style_col);
            $sheet->getStyle('C' . $i)->applyFromArray($style_col);

            $first_cellval_loop = 'D';
            for ($j = 0; $j < $day_num; $j++) {
                $sheet->getStyle($first_cellval_loop . $i)->applyFromArray($style_col);
                $global_cellval_first = $first_cellval_loop;
                $first_cellval_loop++;
            }

            $sheet->getStyle($first_cellval_loop++ . $i)->applyFromArray($style_col);
            $sheet->getStyle($first_cellval_loop++ . $i)->applyFromArray($style_col);
            $sheet->getStyle($first_cellval_loop++ . $i)->applyFromArray($style_col);
            $sheet->getStyle($first_cellval_loop++ . $i)->applyFromArray($style_col);

            $sheet->getStyle('B' . $i)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);


            $i++;
        }

        // Set TTD Principal
        $forstyle = $i + 2;
        $endforstyle = $i + 6;

        $sheet->setCellValue($global_cellval_first . $forstyle, "Mengetahui");
        $sheet->setCellValue($global_cellval_first . $endforstyle, $principal_name);

        $sheet->mergeCells($global_cellval_first . $forstyle . ':' . $global_cellval_end . $forstyle);
        $sheet->mergeCells($global_cellval_first . $endforstyle . ':' . $global_cellval_end . $endforstyle);
        $sheet->getStyle($global_cellval_first . $forstyle . ':' . $global_cellval_end . $endforstyle)->applyFromArray($style_ttd);

        // Set height kolom 
        $sheet->getRowDimension(1)->setRowHeight(50);
        $sheet->getRowDimension(3)->setRowHeight(-1);
        $sheet->getRowDimension(4)->setRowHeight(80);
        // $sheet->getRowDimension($i)->setRowHeight(80);

        // Set orientasi kertas jadi LANDSCAPE
        $sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);

        // Set judul file excel nya
        $sheet->setTitle("Laporan Absensi Peserta Didik");

        // Proses file excel
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="Laporan Absensi Peserta Didik "' . $classname . '".xlsx"'); // Set nama file excel nya
        header('Cache-Control: max-age=0');

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
    }

    public function attendance_teacher()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Laporan - Absensi Guru';
        $data['content'] = 'admin/report/attendance/teacher/index';

        return $this->load->view('master', $data);
    }

    public function attendance_teacher_preview()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $month = getPost('month');

        if (empty($month)) {
            return JSONResponseDefault('FAILED', 'Bulan tidak boleh kosong');
        }

        $day_num = date('t', strtotime($month));

        $data = array();
        $data['month'] = $month;
        $data['amountdate'] = $day_num;
        $data['holiday'] = getSettingsValue('holiday') ?? '';
        $data['teacher'] = $this->msteacher->select('a.id, a.nip, a.name,a.entryschedule')
            ->result(array(
                'a.createdby' => getCurrentIdUser(),
                "(a.status IS NULL OR a.status = 'Active') =" => true,
                'a.isdeleted' => null
            ));

        $teacherid = array_column($data['teacher'], 'id');

        // Ambil semua data izin untuk semua peserta didik di kelas pada bulan tersebut
        $permissions = $this->db->select('id, teacherid, typepermission, startdate, enddate')
            ->from('mspermissionteacher')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('startdate <=', date('Y-m-t', strtotime($month))) // akhir bulan
            ->where('enddate >=', date('Y-m-01', strtotime($month))); // awal bulan

        if (count($teacherid)) {
            $this->db->where_in('teacherid', $teacherid);
        }

        $permissions = $this->db->get()->result();

        // Ambil semua data absensi untuk semua peserta didik di kelas pada bulan tersebut
        $attendance = $this->db->select('teacherid, status, DATE(date) as date')
            ->from('attendanceteacher')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('DATE(date) >=', date('Y-m-01', strtotime($month))) // awal bulan
            ->where('DATE(date) <=', date('Y-m-t', strtotime($month))); // akhir bulan

        if (count($teacherid) > 0) {
            $this->db->where_in('teacherid', $teacherid);
        }

        $attendance = $this->db->get()->result();

        $data['permissions'] = $permissions;
        $data['attendance'] = $attendance;

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('admin/report/attendance/teacher/preview', $data, true)
        ));
    }

    public function attendance_teacher_pdf()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $month = getGet('month');

        if (empty($month)) {
            return redirect(base_url('report/attendance/teacher'));
        }

        $amountdate = date('t', strtotime($month));

        $teacher = $this->msteacher->get(array(
            'createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool(),
            "(status IS NULL OR status = 'Active') =" => true,
            'isdeleted' => null
        ))->result();

        $holiday = getSettingsValue('holiday') ?? [];

        // Ambil semua data izin untuk semua guru pada bulan tersebut
        $permissions = $this->db->select('id, teacherid, typepermission, startdate, enddate')
            ->from('mspermissionteacher')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('startdate <=', date('Y-m-t', strtotime($month))) // akhir bulan
            ->where('enddate >=', date('Y-m-01', strtotime($month))) // awal bulan
            ->where_in('teacherid', array_column($teacher, 'id'))
            ->get()->result();

        // Ambil semua data absensi untuk semua guru pada bulan tersebut
        $attendance = $this->db->select('teacherid, status, DATE(date) as date')
            ->from('attendanceteacher')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('DATE(date) >=', date('Y-m-01', strtotime($month))) // awal bulan
            ->where('DATE(date) <=', date('Y-m-t', strtotime($month))) // akhir bulan
            ->where_in('teacherid', array_column($teacher, 'id'))
            ->get()->result();

        $school = $this->msusers->get(array('id' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->row();

        $html2pdf = new Html2Pdf('L', 'A4', 'en', true, 'UTF-8', array(5, 5, 5, 5));
        $html2pdf->writeHTML($this->load->view('admin/report/attendance/teacher/pdf', array(
            'teacher' => $teacher,
            'permissions' => $permissions,
            'attendance' => $attendance,
            'holiday' => $holiday,
            'month' => $month,
            'amountdate' => $amountdate,
            'school' => $school
        ), true));

        $html2pdf->output();
    }

    public function process_attendance_teacher()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        ini_set('max_execution_time', 0);

        $month = getPost('month');
        $apd_month = date('m', strtotime($month));
        $apd_year = date('Y', strtotime($month));

        $holiday = getSettingsValue('holiday');
        $holiday = explode(',', $holiday);
        $principal_name = getCurrentUser()->principal ?? null;;


        if ($month != null) {
            $day_num = date('t', strtotime($month));
        } else {
            $day_num = date('t');
        }

        $letter = 'D';
        for ($i = 0; $i < $day_num; $i++) {
            if ($day_num - 1 == $i) {
                $last_letter = $letter++;
            } else {
                $letter++;
            }
        }

        $first_cell_amount = $letter;

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $style = [
            'font' => ['bold' => true], // Set font nya jadi bold
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // Set text jadi ditengah secara horizontal (center)
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER // Set text jadi di tengah secara vertical (middle)
            ],
            'borders' => [
                'top' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border top dengan garis tipis
                'right' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],  // Set border right dengan garis tipis
                'bottom' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border bottom dengan garis tipis
                'left' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN] // Set border left dengan garis tipis
            ]
        ];

        // Buat sebuah variabel untuk menampung pengaturan style dari header tabel
        $style_col = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // Set text jadi ditengah secara horizontal (center)
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER // Set text jadi di tengah secara vertical (middle)
            ],
            'borders' => [
                'top' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border top dengan garis tipis
                'right' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],  // Set border right dengan garis tipis
                'bottom' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border bottom dengan garis tipis
                'left' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN] // Set border left dengan garis tipis
            ]
        ];

        // Buat sebuah variabel untuk menampung pengaturan style dari header tabel
        $style_ttd = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // Set text jadi ditengah secara horizontal (center)
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER // Set text jadi di tengah secara vertical (middle)
            ]
        ];

        $sheet->setCellValue('A1', "Laporan Absensi Guru\n" . date('F', strtotime($month)) . " " . $apd_year); // Set kolom A1 dengan tulisan "DATA peserta didik"
        $sheet->mergeCells('A1:AL2');
        $sheet->getStyle('A1')->getAlignment()->setWrapText(true);

        // Set Merge Cell pada kolom A1 sampai E1
        $sheet->getStyle('A1')->applyFromArray($style); // Set bold kolom A1

        // Buat header tabel nya pada baris ke 3
        $sheet->setCellValue('A3', "NO");
        $sheet->setCellValue('B3', "NIP");
        $sheet->setCellValue('C3', "NAMA");
        $sheet->setCellValue('D3', "TANGGAL");
        $sheet->setCellValue($first_cell_amount . '3', "JUMLAH");

        $bkup_cell_amount = $first_cell_amount;
        for ($i = 0; $i <= 3; $i++) {
            $last_cell_amount = $bkup_cell_amount++;
        }

        //Merge header
        $sheet->mergeCells('A3:A5');
        $sheet->mergeCells('B3:B5');
        $sheet->mergeCells('C3:C5');
        $sheet->mergeCells('D3:' . $last_letter . '3');
        $sheet->mergeCells($first_cell_amount . '3:' . $last_cell_amount . '4');

        // Apply style header yang telah kita buat tadi ke masing-masing kolom header
        $sheet->getStyle('A3:A5')->applyFromArray($style_col);
        $sheet->getStyle('B3:B5')->applyFromArray($style_col);
        $sheet->getStyle('C3:C5')->applyFromArray($style_col);
        $sheet->getStyle('D3:' . $last_letter . '3')->applyFromArray($style_col);
        $sheet->getStyle($first_cell_amount . '3:' . $last_cell_amount . '4')->applyFromArray($style_col);

        // Set width kolom
        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(40);
        $sheet->getColumnDimension('C')->setWidth(40);

        $first_coldim = 'D';

        for ($i = 0; $i < $day_num; $i++) {
            $sheet->getColumnDimension($first_coldim)->setWidth(5);
            $first_coldim++;
        }

        $sheet->getColumnDimension($first_coldim++)->setWidth(5);
        $sheet->getColumnDimension($first_coldim++)->setWidth(5);
        $sheet->getColumnDimension($first_coldim++)->setWidth(5);
        $sheet->getColumnDimension($first_coldim++)->setWidth(5);

        $first_cellval = 'D';
        for ($i = 1; $i <= $day_num; $i++) {
            $date = date("$i/m/Y", strtotime($month));
            $sheet->setCellValue($first_cellval . '4', $date);
            $first_cellval++;
        }

        $first_cellstyle = 'D';
        for ($i = 0; $i < $day_num; $i++) {
            $sheet->getStyle($first_cellstyle . '4')->applyFromArray($style_col);
            $sheet->getStyle($first_cellstyle . '4')->getAlignment()->setTextRotation(90);
            $first_cellstyle++;
        }

        $first_cellval = 'D';
        for ($i = 0; $i < $day_num; $i++) {
            $sheet->setCellValue($first_cellval . '5', "1");
            $first_cellval++;
        }

        $sheet->setCellValue($first_cellval++ . '5', "H");
        $sheet->setCellValue($first_cellval++ . '5', "I");
        $sheet->setCellValue($first_cellval++ . '5', "S");
        $sheet->setCellValue($first_cellval++ . '5', "A");

        $first_cellstyle = 'D';
        for ($i = 0; $i < $day_num; $i++) {
            $sheet->getStyle($first_cellstyle . '5')->applyFromArray($style_col);
            $first_cellstyle++;
        }

        $sheet->getStyle($first_cellstyle++ . '5')->applyFromArray($style_col);
        $sheet->getStyle($first_cellstyle++ . '5')->applyFromArray($style_col);
        $sheet->getStyle($first_cellstyle++ . '5')->applyFromArray($style_col);
        $sheet->getStyle($first_cellstyle++ . '5')->applyFromArray($style_col);

        $teacher = $this->msteacher->get(array(
            'createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool(),
            "(status IS NULL OR status = 'Active') =" => true,
            'isdeleted' => null
        ))->result();

        $holiday = getSettingsValue('holiday') ?? [];

        // Ambil semua data izin untuk semua guru pada bulan tersebut
        $permissions = $this->db->select('id, teacherid, typepermission, startdate, enddate')
            ->from('mspermissionteacher')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('startdate <=', date('Y-m-t', strtotime($month))) // akhir bulan
            ->where('enddate >=', date('Y-m-01', strtotime($month))) // awal bulan
            ->where_in('teacherid', array_column($teacher, 'id'))
            ->get()->result();

        // Ambil semua data absensi untuk semua guru pada bulan tersebut
        $attendance = $this->db->select('teacherid, status, DATE(date) as date')
            ->from('attendanceteacher')
            ->where('createdby', isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())
            ->where('DATE(date) >=', date('Y-m-01', strtotime($month))) // awal bulan
            ->where('DATE(date) <=', date('Y-m-t', strtotime($month))) // akhir bulan
            ->where_in('teacherid', array_column($teacher, 'id'))
            ->get()->result();

        $school = $this->msusers->get(array('id' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->row();

        $no = 1;
        $i = 6;
        $global_cellval_first = "AI";
        $global_cellval_end = "AI";

        $holidaySettings = explode(',', getSettingsValue('holiday') ?? '');

        foreach ($teacher as $key => $value) {
            $sheet->setCellValue('A' . $i, $no++);
            $sheet->setCellValue('B' . $i, $value->nip);
            $sheet->setCellValue('C' . $i, $value->name);

            $h = $iz = $s = $a = 0;

            $first_cellval_loop = 'D';

            for ($j = 0; $j < $day_num; $j++) {
                $apd_date = ($j + 1);
                $apd_month = date('m', strtotime($month));
                $apd_year = date('Y', strtotime($month));

                $format = "$apd_year-$apd_month-$apd_date";
                $currentDate = date('Y-m-d', strtotime($format));
                $days = hari_ini(date('D', strtotime(date("Y-m-d", strtotime($month . '-' . $apd_date)))));

                // Cek apakah tanggal ini libur
                $isHoliday = in_array($days, $holidaySettings);
                if (!$isHoliday) {
                    $holidays = explode(',', $value->entryschedule);
                    $isHoliday = !in_array($days, $holidays);
                }

                // Cek apakah peserta didik memiliki izin pada tanggal ini
                $hasPermission = false;
                foreach ($permissions as $permission) {
                    if ($currentDate >= $permission->startdate && $currentDate <= $permission->enddate && $permission->teacherid == $value->id) {
                        $hasPermission = $permission->typepermission;
                        break;
                    }
                }

                // Cek apakah ada absensi pada tanggal ini
                $attendanceStatus = null;
                foreach ($attendance as $att) {
                    if ($att->teacherid == $value->id && $att->date == $currentDate) {
                        $attendanceStatus = $att->status;
                        break;
                    }
                }

                if ($isHoliday) {
                    $sheet->setCellValue($first_cellval_loop . $i, "L");
                } else if ($hasPermission) {
                    if ($hasPermission == 'permission') {
                        $sheet->setCellValue($first_cellval_loop . $i, "I");
                        $iz++;
                    } else {
                        $sheet->setCellValue($first_cellval_loop . $i, "S");
                        $s++;
                    }
                } else if ($attendanceStatus) {
                    if ($attendanceStatus == 'Late' || $attendanceStatus == 'Entry') {
                        $sheet->setCellValue($first_cellval_loop . $i, "H");
                        $h++;
                    }
                } else if (getCurrentDate() > $currentDate) {
                    $sheet->setCellValue($first_cellval_loop . $i, "A");
                    $a++;
                } else {
                    $sheet->setCellValue($first_cellval_loop . $i, "-");
                }

                $first_cellval_loop++;
            }

            $sheet->setCellValue($first_cellval_loop++ . $i, $h == 0 ? '-' : $h);
            $sheet->setCellValue($first_cellval_loop++ . $i, $iz == 0 ? '-' : $iz);
            $sheet->setCellValue($first_cellval_loop++ . $i, $s == 0 ? '-' : $s);
            $global_cellval_end = $first_cellval_loop;
            $sheet->setCellValue($first_cellval_loop++ . $i, $a == 0 ? '-' : $a);

            $sheet->getStyle('A' . $i)->applyFromArray($style_col);
            $sheet->getStyle('B' . $i)->applyFromArray($style_col);
            $sheet->getStyle('C' . $i)->applyFromArray($style_col);

            $first_cellval_loop = 'D';
            for ($j = 0; $j < $day_num; $j++) {
                $sheet->getStyle($first_cellval_loop . $i)->applyFromArray($style_col);
                $global_cellval_first = $first_cellval_loop;
                $first_cellval_loop++;
            }

            $sheet->getStyle($first_cellval_loop++ . $i)->applyFromArray($style_col);
            $sheet->getStyle($first_cellval_loop++ . $i)->applyFromArray($style_col);
            $sheet->getStyle($first_cellval_loop++ . $i)->applyFromArray($style_col);
            $sheet->getStyle($first_cellval_loop++ . $i)->applyFromArray($style_col);

            $sheet->getStyle('B' . $i)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);


            $i++;
        }

        // Set TTD Principal
        $forstyle = $i + 2;
        $endforstyle = $i + 6;

        $sheet->setCellValue($global_cellval_first . $forstyle, "Mengetahui");
        $sheet->setCellValue($global_cellval_first . $endforstyle, $principal_name);

        $sheet->mergeCells($global_cellval_first . $forstyle . ':' . $global_cellval_end . $forstyle);
        $sheet->mergeCells($global_cellval_first . $endforstyle . ':' . $global_cellval_end . $endforstyle);
        $sheet->getStyle($global_cellval_first . $forstyle . ':' . $global_cellval_end . $endforstyle)->applyFromArray($style_ttd);

        // Set height kolom 
        $sheet->getRowDimension(1)->setRowHeight(50);
        $sheet->getRowDimension(3)->setRowHeight(-1);
        $sheet->getRowDimension(4)->setRowHeight(80);
        // $sheet->getRowDimension($i)->setRowHeight(80);

        // Set orientasi kertas jadi LANDSCAPE
        $sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);

        // Set judul file excel nya
        $sheet->setTitle("Laporan Absensi Guru");

        // Proses file excel
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="Laporan Absensi Guru "' . $apd_month . "/" . $apd_year . '".xlsx"'); // Set nama file excel nya
        header('Cache-Control: max-age=0');

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
    }

    public function attendancecourse()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        } else if (!isTeacher() && !isAdmin()) {
            return redirect(base_url('auth/login/teacher'));
        }

        $data = array();
        $data['title'] = 'Laporan - Absensi per Mapel';
        $data['content'] = 'admin/report/attendancecourse/index';

        return $this->load->view('master', $data);
    }

    public function attendancecourse_datatables()
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $date = getPost('date', getCurrentDate('Y-m-d'));

        $datatable = $this->datatables->make('TbAttendancecourse', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        if (isAdmin()) {
            $where['a.createdby'] = getCurrentIdUser();
        } else if (isTeacher()) {
            $where['a.createdby'] = getCurrentIdSchool();
            $where['b.teacherid'] = getCurrentIdUser();
        }

        $where['DATE(a.createddate)'] = $date;

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('report/attendancecourse/student/' . $value->id) . "\" class=\"btn btn-primary btn-sm w-50 ms-2 mt-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"List Peserta Didik\">
                    <i class=\"ti ti-users\"></i>
                </a>

                <a href=\"" . base_url('report/attendancecourse/process/' . $value->id) . "\" class=\"btn btn-success btn-sm w-50 ms-2 mt-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-success\" data-bs-original-title=\"Export Excel\">
                    <i class=\"ti ti-file-spreadsheet\"></i>
                </a>
                <a href=\"" . base_url('report/attendancecourse/export/' . $value->id) . "\" class=\"btn btn-danger btn-sm w-50 ms-2 mt-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Export Pdf\" target=\"_blank\">
                    <i class=\"ti ti-pdf \"></i>
                </a>
            </div>";

            $detail[] = $value->coursename;
            $detail[] = $value->teachername;
            $detail[] = $value->classname;
            $detail[] = tgl_indo($value->customdate);
            $detail[] = $value->starttime . ' - ' . $value->endtime;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function attendancecourse_student($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        } else if (!isTeacher() && !isAdmin()) {
            return redirect(base_url('auth/login/teacher'));
        }

        $getattendancecourse = $this->attendancecourse->select('a.*,DATE(a.createddate) as customdate,b.classid, c.name as classname, d.name as coursename,e.name as teachername,b.starttime,b.endtime')
            ->join('msscheduleclass b', 'b.id = a.scheduleclassid')
            ->join('msclass c', 'c.id = b.classid AND c.isdeleted IS NULL')
            ->join('mscourse d', 'd.id = b.courseid')
            ->join('msteacher e', 'e.id = b.teacherid', 'LEFT')
            ->where(array('a.id' => $id))
            ->get();

        if ($getattendancecourse->num_rows() == 0) {
            return redirect(base_url('dashboard'));
        }

        $rowattendance = $getattendancecourse->row();

        $getstudent = $this->msstudent->get(array(
            'a.classid' => $rowattendance->classid,
            "(a.status IS NULL OR a.status = 'Active') =" => true
        ));

        $studententry = array();
        $studentpermission = array();
        $studentsick = array();
        $studentalpha = array();

        $getattendancedetail = $this->attendancecoursedetail->get(array(
            'a.attendancecourseid' => $id,
        ))->result();

        foreach ($getstudent->result() as $key => $value) {
            if (in_array($value->id, array_column($getattendancedetail, 'studentid'))) {
                if ($getattendancedetail[array_search($value->id, array_column($getattendancedetail, 'studentid'))]->status == 'Entry') {
                    $studententry[$value->id] = 'checked';
                } else if ($getattendancedetail[array_search($value->id, array_column($getattendancedetail, 'studentid'))]->status == 'Permission') {
                    $studentpermission[$value->id] = 'checked';
                } else if ($getattendancedetail[array_search($value->id, array_column($getattendancedetail, 'studentid'))]->status == 'Sick') {
                    $studentsick[$value->id] = 'checked';
                } else if ($getattendancedetail[array_search($value->id, array_column($getattendancedetail, 'studentid'))]->status == 'Alpha') {
                    $studentalpha[$value->id] = 'checked';
                }
            }
        }

        $data = array();
        $data['title'] = 'Absensi Mapel Peserta Didik';
        $data['content'] = 'admin/report/attendancecourse/student';
        $data['id'] = $id;
        $data['schedule'] = $rowattendance;
        $data['student'] = $this->msstudent->select('*')
            ->where(
                array(
                    'classid' => $rowattendance->classid,
                    "(a.status IS NULL OR a.status = 'Active') =" => true
                )
            )->order_by('name')->result();
        $data['studententry'] = $studententry;
        $data['studentpermission'] = $studentpermission;
        $data['studentsick'] = $studentsick;
        $data['studentalpha'] = $studentalpha;

        return $this->load->view('master', $data);
    }

    public function process_attendancecourse($id)
    {
        if (!isLogin() || (!isTeacher() && !isAdmin())) {
            return redirect(base_url('auth/login/admin'));
        }

        ini_set('max_execution_time', 0);

        $principal_name = getCurrentUser(isAdmin() ? getCurrentIdUser() : getCurrentIdSchool())->principal ?? null;

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $style = [
            'font' => ['bold' => true], // Set font nya jadi bold
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // Set text jadi ditengah secara horizontal (center)
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER // Set text jadi di tengah secara vertical (middle)
            ],
            'borders' => [
                'top' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border top dengan garis tipis
                'right' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],  // Set border right dengan garis tipis
                'bottom' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border bottom dengan garis tipis
                'left' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN] // Set border left dengan garis tipis
            ]
        ];

        // Buat sebuah variabel untuk menampung pengaturan style dari header tabel
        $style_col = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // Set text jadi ditengah secara horizontal (center)
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER // Set text jadi di tengah secara vertical (middle)
            ],
            'borders' => [
                'top' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border top dengan garis tipis
                'right' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN],  // Set border right dengan garis tipis
                'bottom' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN], // Set border bottom dengan garis tipis
                'left' => ['borderStyle'  => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN] // Set border left dengan garis tipis
            ]
        ];

        // Buat sebuah variabel untuk menampung pengaturan style dari header tabel
        $style_ttd = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER, // Set text jadi ditengah secara horizontal (center)
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER // Set text jadi di tengah secara vertical (middle)
            ]
        ];

        $getattendance = $this->attendancecourse->select('a.*,DATE(a.createddate) as customdate,b.classid, c.name as classname, d.name as coursename,e.name as teachername,b.starttime,b.endtime,g.nis, g.name as studentname, f.status')
            ->join('msscheduleclass b', 'b.id = a.scheduleclassid')
            ->join('msclass c', 'c.id = b.classid')
            ->join('mscourse d', 'd.id = b.courseid')
            ->join('msteacher e', 'e.id = b.teacherid', 'LEFT')
            ->join('attendancecoursedetail f', 'f.attendancecourseid = a.id')
            ->join('msstudent g', 'g.id = f.studentid')
            ->where(array('a.id' => $id))
            ->order_by('g.name')
            ->get();

        if ($getattendance->num_rows() > 0) {
            $rowattendance = $getattendance->row();
            $coursename = $rowattendance->coursename;
            $classname = $rowattendance->classname;
            $teachername = $rowattendance->teachername;
            $starttime = $rowattendance->starttime;
            $endtime = $rowattendance->endtime;
        } else {
            $coursename = '';
            $classname = '';
            $teachername = '';
            $starttime = '';
            $endtime = '';
        }

        $sheet->setCellValue('A1', "LAPORAN ABSENSI MATA PELAJARAN " . $coursename . " KELAS " . $classname . " \n" . " GURU PENGAJAR :  " . $teachername . " \n" . tgl_indo(getCurrentDate('Y-m-d')) . " " . $starttime . " - " . $endtime); // Set kolom A1 dengan tulisan "DATA peserta didik"
        $sheet->mergeCells('A1:G2');
        $sheet->getStyle('A1')->getAlignment()->setWrapText(true);

        // Set Merge Cell pada kolom A1 sampai E1
        $sheet->getStyle('A1')->applyFromArray($style); // Set bold kolom A1

        // Buat header tabel nya pada baris ke 3
        $sheet->setCellValue('A3', "NO");
        $sheet->setCellValue('B3', "NIS");
        $sheet->setCellValue('C3', "NAMA");
        $sheet->setCellValue('D3', "KETERANGAN");

        //Merge header
        $sheet->mergeCells('A3:A5');
        $sheet->mergeCells('B3:B5');
        $sheet->mergeCells('C3:C5');
        $sheet->mergeCells('D3:G4');

        // Apply style header yang telah kita buat tadi ke masing-masing kolom header
        $sheet->getStyle('A3:A5')->applyFromArray($style_col);
        $sheet->getStyle('B3:B5')->applyFromArray($style_col);
        $sheet->getStyle('C3:C5')->applyFromArray($style_col);

        $sheet->getStyle('D3:G4')->applyFromArray($style_col)->getAlignment()->setWrapText(true);

        // Set width kolom
        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(40);
        $sheet->getColumnDimension('C')->setWidth(40);


        $sheet->setCellValue('D5', "H");
        $sheet->setCellValue('E5', "I");
        $sheet->setCellValue('F5', "S");
        $sheet->setCellValue('G5', "A");

        $sheet->getStyle('D5')->applyFromArray($style_col);
        $sheet->getStyle('E5')->applyFromArray($style_col);
        $sheet->getStyle('F5')->applyFromArray($style_col);
        $sheet->getStyle('G5')->applyFromArray($style_col);

        $no = 1;
        $i = 6;
        $global_cellval_first = "D";
        $global_cellval_end = "G";
        foreach ($getattendance->result() as $key => $value) {
            $sheet->setCellValue('A' . $i, $no++);
            $sheet->setCellValue('B' . $i, $value->nis);
            $sheet->setCellValue('C' . $i, $value->studentname);

            if ($value->status == 'Entry') {
                $sheet->setCellValue('D' . $i, 'H');
                $sheet->setCellValue('E' . $i, '-');
                $sheet->setCellValue('F' . $i, '-');
                $sheet->setCellValue('G' . $i, '-');
            } else if ($value->status == 'Permission') {
                $sheet->setCellValue('D' . $i, '-');
                $sheet->setCellValue('E' . $i, 'I');
                $sheet->setCellValue('F' . $i, '-');
                $sheet->setCellValue('G' . $i, '-');
            } else if ($value->status == 'Sick') {
                $sheet->setCellValue('D' . $i, '-');
                $sheet->setCellValue('E' . $i, '-');
                $sheet->setCellValue('F' . $i, 'S');
                $sheet->setCellValue('G' . $i, '-');
            } else if ($value->status == 'Alpha') {
                $sheet->setCellValue('D' . $i, '-');
                $sheet->setCellValue('E' . $i, '-');
                $sheet->setCellValue('F' . $i, '-');
                $sheet->setCellValue('G' . $i, 'A');
            }

            $sheet->getStyle('A' . $i)->applyFromArray($style_col);
            $sheet->getStyle('B' . $i)->applyFromArray($style_col);
            $sheet->getStyle('C' . $i)->applyFromArray($style_col);
            $sheet->getStyle('D' . $i)->applyFromArray($style_col);
            $sheet->getStyle('E' . $i)->applyFromArray($style_col);
            $sheet->getStyle('F' . $i)->applyFromArray($style_col);
            $sheet->getStyle('G' . $i)->applyFromArray($style_col);

            $sheet->getStyle('B' . $i)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);

            $i++;
        }

        // Set TTD Principal
        $forstyle = $i + 2;
        $endforstyle = $i + 6;

        $sheet->setCellValue($global_cellval_first . $forstyle, "Mengetahui");
        $sheet->setCellValue($global_cellval_first . $endforstyle, $principal_name);

        $sheet->mergeCells($global_cellval_first . $forstyle . ':' . $global_cellval_end . $forstyle);
        $sheet->mergeCells($global_cellval_first . $endforstyle . ':' . $global_cellval_end . $endforstyle);
        $sheet->getStyle($global_cellval_first . $forstyle . ':' . $global_cellval_end . $endforstyle)->applyFromArray($style_ttd);

        // Set height kolom 
        $sheet->getRowDimension(1)->setRowHeight(50);
        $sheet->getRowDimension(3)->setRowHeight(-1);
        $sheet->getRowDimension(4)->setRowHeight(10);
        // $sheet->getRowDimension($i)->setRowHeight(80);

        // Set orientasi kertas jadi LANDSCAPE
        $sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);

        // Set judul file excel nya
        $sheet->setTitle("LAPORAN ABSENSI MAPEL");

        // Proses file excel
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="LAPORAN ABSENSI MAPEL.xlsx"'); // Set nama file excel nya
        header('Cache-Control: max-age=0');

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
    }

    public function attendancecourse_export_pdf($id)
    {

        if (!isLogin() || (!isTeacher() && !isAdmin())) {
            return redirect(base_url('auth/login/admin'));
        }

        ob_start();

        $getattendance = $this->attendancecourse->select('a.*, DATE(a.createddate) as customdate, b.classid, c.name as classname, d.name as coursename, e.name as teachername, b.starttime, b.endtime, g.nis as studentnis, g.name as studentname, f.status, f.studentid')
            ->join('msscheduleclass b', 'b.id = a.scheduleclassid')
            ->join('msclass c', 'c.id = b.classid')
            ->join('mscourse d', 'd.id = b.courseid')
            ->join('msteacher e', 'e.id = b.teacherid', 'LEFT')
            ->join('attendancecoursedetail f', 'f.attendancecourseid = a.id')
            ->join('msstudent g', 'g.id = f.studentid')
            ->where(array('a.id' => $id))
            ->order_by('g.name')
            ->get()
            ->result();

        $studententry = array();
        $studentpermission = array();
        $studentsick = array();
        $studentalpha = array();

        foreach ($getattendance as $attendance) {
            switch ($attendance->status) {
                case 'Entry':
                    $studententry[$attendance->studentid] = 'checked';
                    break;
                case 'Permission':
                    $studentpermission[$attendance->studentid] = 'checked';
                    break;
                case 'Sick':
                    $studentsick[$attendance->studentid] = 'checked';
                    break;
                case 'Alpha':
                    $studentalpha[$attendance->studentid] = 'checked';
                    break;
            }
        }

        $school = $this->msusers->get(array('id' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->row();

        $html2pdf = new Html2Pdf('P', 'A4', 'en', true, 'UTF-8', array(5, 5, 5, 5));
        $html2pdf->writeHTML($this->load->view('admin/report/attendancecourse/pdf', array(
            'attendance' => $getattendance,
            'school' => $school,
            'studententry' => $studententry,
            'studentpermission' => $studentpermission,
            'studentsick' => $studentsick,
            'studentalpha' => $studentalpha
        ), true));

        ob_end_clean();
        $html2pdf->output();
    }

    public function export_achievement()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        ob_start();

        $classid = getGet('classid');
        $studentid = getGet('studentid');
        $month = getGet('month');

        $g_student = !empty($studentid) ? $this->msstudent->get(array('id' => $studentid))->row() : null;


        $getclass = $this->msclass->get(array('id' => $classid));

        if ($getclass->num_rows() == 0) {
            return redirect(base_url('report/achievement'));
        }

        $rowclass = $getclass->row();

        if (!empty($studentid) && $g_student == null) {
            return redirect(base_url('report/achievement'));
        }

        $school = $this->msusers->get(array('id' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->row();

        // Query Achievement
        $this->db->select('a.*, b.point AS achievementpoint, c.name as studentname, c.nis')
            ->from('achievement a')
            ->join('msachievementpoint b', 'b.id = a.achievementpointid', 'left')
            ->join('msstudent c', 'c.id = a.studentid', 'left')
            ->where('MONTH(a.date)', date('m', strtotime($month)))
            ->where('YEAR(a.date)', date('Y', strtotime($month)))
            ->where('c.classid', $classid);

        if (!empty($studentid)) {
            $this->db->where('a.studentid', $studentid);
        }

        $achievements = $this->db->order_by('a.createddate', 'ASC')->get()->result();

        $nodata = empty($achievements);

        $html2pdf = new Html2Pdf('P', 'A4', 'en', true, 'UTF-8', array(5, 5, 5, 5));
        $html2pdf->writeHTML($this->load->view('admin/report/achievement/pdf', array(
            'classid' => $classid,
            'student' => $g_student,
            'month' => $month,
            'school' => $school,
            'achievement' => $achievements,
            'class' => $rowclass,
            'nodata' => $nodata
        ), true));

        ob_end_clean();
        $html2pdf->output();
    }

    public function achievement_student_preview()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $classid = getPost('classid');
        $month = getPost('month');
        $studentid = getPost('studentid');
        $day_num = date('t', strtotime($month));

        if (empty($classid)) {
            return JSONResponseDefault('FAILED', 'Kelas tidak boleh kosong');
        }

        $data = array();
        $data['student'] = $this->msstudent->get(array(
            'classid' => $classid,
            'createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool(),
            "(status IS NULL OR status = 'Active') =" => null
        ))->result();

        $achievement = $this->db->select('a.id, a.studentid, c.name as studentname, c.nis, a.date, a.description, b.point')
            ->from('achievement a')
            ->join('msachievementpoint b', 'a.achievementpointid = b.id', 'left')
            ->join('msstudent c', 'a.studentid = c.id', 'left')
            ->where('DATE(a.date) >=', date('Y-m-01', strtotime($month)))
            ->where('DATE(a.date) <=', date('Y-m-t', strtotime($month)))
            ->where('c.classid', $classid);

        if (!empty($studentid)) {
            if (is_array($studentid)) {
                $this->db->where_in('a.studentid', $studentid);
            } else {
                $this->db->where('a.studentid', $studentid);
            }
        }

        $data['achievements'] = $this->db->get()->result();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('admin/report/achievement/preview', $data, true)
        ));
    }

    public function process_achievement_student()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        ini_set('max_execution_time', 0);

        $classid = getPost('classid');
        $studentid = getPost('studentid');
        $month = getPost('month');

        $principal_name = getCurrentUser()->principal ?? null;

        $getclass = $this->msclass->get(array('id' => $classid));

        if ($getclass->num_rows() == 0) {
            return redirect(base_url('report/achievement/'));
        }

        $rowclass = $getclass->row();
        $classname = ($rowclass->level ?? null) != null ? $rowclass->level . ' - ' . $rowclass->name : $rowclass->name;

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        if (!empty($studentid)) {
            $student = $this->msstudent->get(array('id' => $studentid))->row();
            $student_name = $student->name ?? "Unknown";
            $title = "Laporan Prestasi $student_name Bulan " . date('F Y', strtotime($month));
        } else {
            $title = "Laporan Prestasi Siswa Kelas $classname Bulan " . date('F Y', strtotime($month));
        }

        // Styke Header Tabel
        $style_header = [
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]
            ]
        ];

        // Format Isi Tabel
        $style_content = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]
            ]
        ];

        // Format TTD
        $style_ttd = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ]
        ];

        // Set Header
        $sheet->setCellValue('A1', $title);
        $sheet->mergeCells('A1:G2');
        $sheet->getStyle('A1')->applyFromArray($style_header);
        $sheet->getStyle('A1')->getFont()->setSize(14);

        if (!empty($studentid)) {
            $headers = ['NO', 'TANGGAL', 'DESKRIPSI', 'POIN'];
        } else {
            $headers = ['NO', 'NIS', 'NAMA SISWA', 'TANGGAL', 'DESKRIPSI', 'POIN'];
        }

        // Set Header ke Excel
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '3', $header);
            $sheet->getStyle($col . '3')->applyFromArray($style_header);
            $sheet->getColumnDimension($col)->setAutosize(true);
            $col++;
        }

        $achievementQuery = $this->db->select('a.date, b.nis, b.name as student_name, a.description, c.point')
            ->from('achievement a')
            ->join('msachievementpoint c', 'a.achievementpointid = c.id', 'left')
            ->join('msstudent b', 'a.studentid = b.id', 'left')
            ->where('b.classid', $classid)
            ->where('DATE(a.date) >=', date('Y-m-01', strtotime($month)))
            ->where('DATE(a.date) <=', date('Y-m-t', strtotime($month)));

        if (!empty($studentid)) {
            $achievementQuery->where('a.studentid', $studentid);
        }

        $achievements = $achievementQuery->get()->result();

        $row = 4; // Baris awal
        foreach ($achievements as $key => $achievement) {
            $sheet->setCellValue('A' . $row, $key + 1);

            $colIndex = 'B';

            if (empty($studentid)) {
                $sheet->setCellValueExplicit($colIndex . $row, $achievement->nis, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);
                $sheet->getColumnDimension($colIndex)->setWidth(15); // Lebar NIS
                $colIndex++;

                $sheet->setCellValue($colIndex . $row, $achievement->student_name);
                $sheet->getColumnDimension($colIndex)->setWidth(30); // Lebar Nama Siswa
                $colIndex++;
            }

            $sheet->setCellValue($colIndex . $row, date('d-m-Y', strtotime($achievement->date)));
            $sheet->getColumnDimension($colIndex)->setWidth(12); // Lebar Tanggal
            $colIndex++;

            // Kolom Deskripsi
            $sheet->setCellValue($colIndex . $row, $achievement->description);
            $sheet->getColumnDimension($colIndex)->setWidth(40);
            $sheet->getColumnDimension($colIndex)->setAutoSize(false);  // Lebar Deskripsi tetap
            $sheet->getStyle($colIndex . $row)->getAlignment()->setWrapText(true);

            $colIndex++;

            // Kolom Poin
            $sheet->setCellValue($colIndex . $row, $achievement->point);
            $sheet->getColumnDimension($colIndex)->setWidth(10); // Lebar Poin

            // Atur tinggi baris otomatis agar teks turun ke bawah
            $sheet->getRowDimension($row)->setRowHeight(-1);

            foreach ($achievements as $index => $achievement) {
                $rowNumber = $index + 2; // Sesuaikan dengan baris data
                $sheet->getRowDimension($rowNumber)->setRowHeight(-1);
            }

            // Terapkan Style Isi
            foreach (range('A', $colIndex) as $column) {
                $sheet->getStyle($column . $row)->applyFromArray($style_content);
            }

            $row++;
        }

        // Atur lebar kolom agar tampilan lebih rapi
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setWidth(45);
        }

        // Jika tidak ada data, tambahkan pesan & atur tampilan agar tidak dempet
        if (count($achievements) == 0) {
            // Menentukan rentang merge sesuai jumlah kolom
            $mergeRange = empty($studentid) ? "A4:F4" : "A4:D4";

            // Menggabungkan sel untuk menampilkan pesan    
            $sheet->mergeCells($mergeRange);
            $sheet->setCellValue("A4", "Tidak ada laporan prestasi pada bulan ini.");

            // Terapkan style agar teks berada di tengah
            $sheet->getStyle($mergeRange)->applyFromArray($style_content);
            $sheet->getStyle($mergeRange)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle($mergeRange)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

            // Atur tinggi baris agar tidak terlalu dempet
            $sheet->getRowDimension(4)->setRowHeight(25);

            $sheet->getColumnDimension('A')->setAutoSize(false);
            $sheet->getColumnDimension('B')->setAutoSize(false);
            $sheet->getColumnDimension('C')->setAutoSize(false);
            $sheet->getColumnDimension('D')->setAutoSize(false);
            $sheet->getColumnDimension('E')->setAutoSize(false);
            $sheet->getColumnDimension('F')->setAutoSize(false);

            // ✅ Atur width masing-masing kolom agar header rapi
            $sheet->getColumnDimension('A')->setWidth(5);   // NO
            $sheet->getColumnDimension('B')->setWidth(15);  // NIS
            $sheet->getColumnDimension('C')->setWidth(25);  // NAMA SISWA
            $sheet->getColumnDimension('D')->setWidth(15);  // TANGGAL
            $sheet->getColumnDimension('E')->setWidth(40);  // DESKRIPSI (Lebar karena teks panjang)
            $sheet->getColumnDimension('F')->setWidth(10);  // POIN

            // Jika studentid kosong, tambahkan kolom G
            if (empty($studentid)) {
                $sheet->getColumnDimension('G')->setAutoSize(false);
                $sheet->getColumnDimension('G')->setWidth(12); // Tambahan kolom
            }
        }


        $signature_row = $row + 2;

        // Tentukan posisi tanda tangan berdasarkan student ID
        if (!empty($studentid)) {
            $start_col = "D"; // Jika student ID ada, tanda tangan di D - E
            $end_col = "E";
        } else {
            $start_col = "F"; // Jika tidak, tetap di F - G
            $end_col = "G";
        }

        $sheet->setCellValue("{$start_col}{$signature_row}", "Mengetahui");
        $sheet->setCellValue("{$start_col}" . ($signature_row + 4), $principal_name);
        $sheet->mergeCells("{$start_col}{$signature_row}:{$end_col}{$signature_row}");
        $sheet->mergeCells("{$start_col}" . ($signature_row + 4) . ":{$end_col}" . ($signature_row + 4));

        $sheet->getStyle("{$start_col}{$signature_row}:{$end_col}" . ($signature_row + 4))->applyFromArray($style_ttd);
        $sheet->getStyle("{$start_col}" . ($signature_row + 4))->getAlignment()->setWrapText(true);

        $sheet->getColumnDimension('E')->setWidth(20);

        // Judul File
        $filename = (!empty($studentid) ? "Laporan Prestasi " . $student_name : "Laporan Prestasi Siswa Kelas " . $classname) . " - " . date('F Y', strtotime($month)) . ".xlsx";

        // Proses output file
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
    }

    public function export_problem()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        ob_start();

        $classid = getGet('classid');
        $studentid = getGet('studentid');
        $month = getGet('month');

        $g_student = !empty($studentid) ? $this->msstudent->get(['id' => $studentid])->row() : null;

        $getclass = $this->msclass->get(['id' => $classid]);

        if ($getclass->num_rows() == 0) {
            return redirect(base_url('report/problem/'));
        }

        $rowclass = $getclass->row();

        if (!empty($studentid) && $g_student == null) {
            return redirect(base_url('report/problem'));
        }

        $school = $this->msusers->get(['id' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()])->row();

        // Query masalah siswa
        $this->db->select('a.*, b.point AS problempoint, c.name as studentname, c.nis')
            ->from('problem a')
            ->join('msproblempoint b', 'b.id = a.problempointid', 'left')
            ->join('msstudent c', 'c.id = a.studentid', 'left')
            ->where('MONTH(a.date)', date('m', strtotime($month)))
            ->where('YEAR(a.date)', date('Y', strtotime($month)))
            ->where('c.classid', $classid);

        if (!empty($studentid)) {
            $this->db->where('a.studentid', $studentid);
        }

        $problems = $this->db->order_by('a.createddate', 'ASC')->get()->result();

        $nodata = empty($problems);

        $html2pdf = new Html2Pdf('P', 'A4', 'en', true, 'UTF-8', array(5, 5, 5, 5));
        $html2pdf->writeHTML($this->load->view('admin/report/problem/pdf', [
            'classid'  => $classid,
            'student'  => $g_student,
            'month'    => $month,
            'school'   => $school,
            'problem'  => $problems,
            'class'    => $rowclass,
            'nodata'   => $nodata // Tambahkan flag untuk pengecekan di View
        ], true));

        ob_end_clean();
        $html2pdf->output();
    }


    public function problem_student_preview()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $classid = getPost('classid');
        $month = getPost('month');
        $studentid = getPost('studentid');
        $day_num = date('t', strtotime($month));

        if (empty($classid)) {
            return JSONResponseDefault('FAILED', 'Kelas tidak boleh kosong');
        }

        $data = array();
        $data['student'] = $this->msstudent->get(array(
            'classid' => $classid,
            'createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool(),
            "(status IS NULL OR status = 'Active') =" => null
        ))->result();

        // $studentid = array_column($data['student'], 'id');

        $problem = $this->db->select('a.id, a.studentid, c.name as studentname, c.nis, a.date, a.description, b.point')
            ->from('problem a')
            ->join('msproblempoint b', 'a.problempointid = b.id', 'left')
            ->join('msstudent c', 'a.studentid = c.id', 'left')
            ->where('DATE(a.date) >=', date('Y-m-01', strtotime($month)))
            ->where('DATE(a.date) <=', date('Y-m-t', strtotime($month)))
            ->where('c.classid', $classid);

        if (!empty($studentid)) {
            if (is_array($studentid)) {
                $this->db->where_in('a.studentid', $studentid); // Jika array
            } else {
                $this->db->where('a.studentid', $studentid); // Jika single value
            }
        }

        $data['problems'] = $this->db->get()->result();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('admin/report/problem/preview', $data, true)
        ));
    }

    public function process_problem_student()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        ini_set('max_execution_time', 0);

        $classid = getPost('classid');
        $studentid = getPost('studentid');
        $month = getPost('month');

        $principal_name = getCurrentUser()->principal ?? null;

        $getclass = $this->msclass->get(['id' => $classid]);

        if ($getclass->num_rows() == 0) {
            return redirect(base_url('report/problem/'));
        }

        $rowclass = $getclass->row();
        $classname = ($rowclass->level ?? null) != null ? $rowclass->level . ' - ' . $rowclass->name : $rowclass->name;

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Judul Laporan Dinamis
        if (!empty($studentid)) {
            $student = $this->msstudent->get(['id' => $studentid])->row();
            $student_name = $student->name ?? "Unknown";
            $title = "Laporan Permasalahan $student_name Bulan " . date('F Y', strtotime($month));
        } else {
            $title = "Laporan Permasalahan Siswa Kelas $classname Bulan " . date('F Y', strtotime($month));
        }

        // Style Header Tabel
        $style_header = [
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]
            ]
        ];

        // Format Isi Tabel
        $style_content = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]
            ]
        ];

        // Format TTD
        $style_ttd = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ]
        ];

        // Set Header
        $sheet->setCellValue('A1', $title);
        $sheet->mergeCells('A1:G2');
        $sheet->getStyle('A1')->applyFromArray($style_header);
        $sheet->getStyle('A1')->getFont()->setSize(14);

        // Header Tabel Dinamis
        if (!empty($studentid)) {
            $headers = ['NO', 'TANGGAL', 'DESKRIPSI', 'POIN'];
        } else {
            $headers = ['NO', 'NIS', 'NAMA SISWA', 'TANGGAL', 'DESKRIPSI', 'POIN'];
        }

        // Set Header ke Excel
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '3', $header);
            $sheet->getStyle($col . '3')->applyFromArray($style_header);
            $sheet->getColumnDimension($col)->setAutoSize(true);
            $col++;
        }

        // Query Permasalahan
        $problemsQuery = $this->db->select('p.date, s.nis, s.name AS student_name, p.description, pp.point')
            ->from('problem p')
            ->join('msproblempoint pp', 'p.problempointid = pp.id', 'left')
            ->join('msstudent s', 'p.studentid = s.id', 'left')
            ->where('s.classid', $classid)
            ->where('DATE(p.date) >=', date('Y-m-01', strtotime($month)))
            ->where('DATE(p.date) <=', date('Y-m-t', strtotime($month)));

        if (!empty($studentid)) {
            $problemsQuery->where('p.studentid', $studentid);
        }

        $problems = $problemsQuery->get()->result();

        $row = 4; // Baris awal
        foreach ($problems as $key => $problem) {
            $sheet->setCellValue('A' . $row, $key + 1);

            $colIndex = 'B';

            if (empty($studentid)) {
                $sheet->setCellValueExplicit($colIndex . $row, $problem->nis, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);
                $sheet->getColumnDimension($colIndex)->setWidth(15); // Lebar NIS
                $colIndex++;

                $sheet->setCellValue($colIndex . $row, $problem->student_name);
                $sheet->getColumnDimension($colIndex)->setWidth(30); // Lebar Nama Siswa
                $colIndex++;
            }

            $sheet->setCellValue($colIndex . $row, date('d-m-Y', strtotime($problem->date)));
            $sheet->getColumnDimension($colIndex)->setWidth(12); // Lebar Tanggal
            $colIndex++;

            // Kolom Deskripsi
            $sheet->setCellValue($colIndex . $row, $problem->description);
            $sheet->getColumnDimension($colIndex)->setWidth(40);
            $sheet->getColumnDimension($colIndex)->setAutoSize(false);  // Lebar Deskripsi tetap
            $sheet->getStyle($colIndex . $row)->getAlignment()->setWrapText(true);

            $colIndex++;

            // Kolom Poin
            $sheet->setCellValue($colIndex . $row, $problem->point);
            $sheet->getColumnDimension($colIndex)->setWidth(10); // Lebar Poin

            // Atur tinggi baris otomatis agar teks turun ke bawah
            $sheet->getRowDimension($row)->setRowHeight(-1);

            foreach ($problems as $index => $problem) {
                $rowNumber = $index + 2; // Sesuaikan dengan baris data
                $sheet->getRowDimension($rowNumber)->setRowHeight(-1);
            }

            // Terapkan Style Isi
            foreach (range('A', $colIndex) as $column) {
                $sheet->getStyle($column . $row)->applyFromArray($style_content);
            }

            $row++;
        }

        // Atur lebar kolom agar tampilan lebih rapi
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setWidth(40);
        }

        // Jika tidak ada data, tambahkan pesan & atur tampilan agar tidak dempet
        if (count($problems) == 0) {
            // Menentukan rentang merge sesuai jumlah kolom
            $mergeRange = empty($studentid) ? "A4:F4" : "A4:D4";

            // Menggabungkan sel untuk menampilkan pesan    
            $sheet->mergeCells($mergeRange);
            $sheet->setCellValue("A4", "Tidak ada laporan permasalahan pada bulan ini.");

            // Terapkan style agar teks berada di tengah
            $sheet->getStyle($mergeRange)->applyFromArray($style_content);
            $sheet->getStyle($mergeRange)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle($mergeRange)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

            // Atur tinggi baris agar tidak terlalu dempet
            $sheet->getRowDimension(4)->setRowHeight(25);

            $sheet->getColumnDimension('A')->setAutoSize(false);
            $sheet->getColumnDimension('B')->setAutoSize(false);
            $sheet->getColumnDimension('C')->setAutoSize(false);
            $sheet->getColumnDimension('D')->setAutoSize(false);
            $sheet->getColumnDimension('E')->setAutoSize(false);
            $sheet->getColumnDimension('F')->setAutoSize(false);

            // ✅ Atur width masing-masing kolom agar header rapi
            $sheet->getColumnDimension('A')->setWidth(5);   // NO
            $sheet->getColumnDimension('B')->setWidth(15);  // NIS
            $sheet->getColumnDimension('C')->setWidth(25);  // NAMA SISWA
            $sheet->getColumnDimension('D')->setWidth(15);  // TANGGAL
            $sheet->getColumnDimension('E')->setWidth(40);  // DESKRIPSI (Lebar karena teks panjang)
            $sheet->getColumnDimension('F')->setWidth(10);  // POIN

            // Jika studentid kosong, tambahkan kolom G
            if (empty($studentid)) {
                $sheet->getColumnDimension('G')->setAutoSize(false);
                $sheet->getColumnDimension('G')->setWidth(12); // Tambahan kolom
            }
        }


        // TTD Principal
        $signature_row = $row + 2;

        // Tentukan posisi tanda tangan berdasarkan student ID
        if (!empty($studentid)) {
            $start_col = "D"; // Jika student ID ada, tanda tangan di D - E
            $end_col = "E";
        } else {
            $start_col = "F"; // Jika tidak, tetap di F - G
            $end_col = "G";
        }

        $sheet->setCellValue("{$start_col}{$signature_row}", "Mengetahui");
        $sheet->setCellValue("{$start_col}" . ($signature_row + 4), $principal_name);
        $sheet->mergeCells("{$start_col}{$signature_row}:{$end_col}{$signature_row}");
        $sheet->mergeCells("{$start_col}" . ($signature_row + 4) . ":{$end_col}" . ($signature_row + 4));

        $sheet->getStyle("{$start_col}{$signature_row}:{$end_col}" . ($signature_row + 4))->applyFromArray($style_ttd);
        $sheet->getStyle("{$start_col}" . ($signature_row + 4))->getAlignment()->setWrapText(true);

        $sheet->getColumnDimension('E')->setWidth(20);

        // Set Judul File
        $filename = (!empty($studentid) ? "Laporan Permasalahan " . $student_name : "Laporan Permasalahan Siswa Kelas " . $classname) . " - " . date('F Y', strtotime($month)) . ".xlsx";

        // Proses Output File
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
    }
}
