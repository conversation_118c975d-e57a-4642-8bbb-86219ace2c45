<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <style>
        table {
            border-collapse: collapse;
            width: 94%;
        }

        table,
        th,
        td {
            border: 1px solid black;
        }

        th,
        td {
            padding: 6px;
            text-align: center;
            font-size: 10.5px;
        }

        table td.nama-pesertadidik {
            word-wrap: break-word !important;
            /* Membungkus kata yang panjang */
            word-break: break-all !important;
            /* Memecah kata di mana saja jika diperlukan */
            white-space: normal !important;
            /* Mengizinkan teks untuk membungkus */
            max-width: 160px !important;
            width: 160px;
            /* Mengatur lebar maksimum kolom */
            overflow-wrap: break-word !important;
            /* Membungkus kata yang panjang */
        }

        table th.nama-pesertadidik {
            word-wrap: break-word !important;
            /* Membungkus kata yang panjang */
            word-break: break-all !important;
            /* Memecah kata di mana saja jika diperlukan */
            white-space: normal !important;
            /* Mengizinkan teks untuk membungkus */
            max-width: 160px !important;
            width: 160px;
            /* Mengatur lebar maksimum kolom */
            overflow-wrap: break-word !important;
            /* Membungkus kata yang panjang */
        }



        th {
            background-color: #007BFF;
            color: white;
        }

        .header-table {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 20px;
            width: 100%;
        }

        .header-table table {
            width: 100%;
            border: none;
        }

        .header-table td {
            border: none;
            padding: 5px;
            vertical-align: middle;
        }

        .content-table th {
            background-color: #007BFF;
            color: white;
        }

        .content-table td {
            color: #1D2939;
            font-size: 12px;
        }

        .bg-primary {
            background-color: #007BFF;
            color: white;
        }

        .bg-warning {
            background-color: #FFC107;
            color: white;
        }

        .bg-success {
            background-color: #28A745;
            color: white;
        }

        .bg-danger {
            background-color: #DC3545;
            color: white;
        }
    </style>
</head>

<body>
    <div class="header-table">
        <table style="margin: 0 auto;">
            <tr>
                <td style="width: 15%; text-align: center;">
                    <?php if ($school->logo == null): ?>
                        <img src="<?= convertImageToBase64('./assets/smartschool-project.jpg') ?>" alt="Logo Sekolah" width="80">
                    <?php else: ?>
                        <img src="<?= convertImageToBase64('./uploads/logo/' . $school->logo) ?>" alt="Logo Sekolah" width="80">
                    <?php endif; ?>
                </td>

                <td style="width: 70%; text-align: center;">
                    <h2 style="font-size: 18px; margin: 0;"><?= strtoupper($school->name) ?></h2>
                    <p style="font-size: 12px; margin: 0;"><?= $school->address ?? null ?></p>
                    <p style="font-size: 12px; margin: 0;">Telepon: <?= $school->phonenumber ?? '-' ?> | Email: <?= $school->schoolemail ?? '-' ?></p>
                </td>

                <td style="width: 15%; text-align: center;">
                    <?php if ($school->logo_institution == null): ?>
                        <img src="<?= convertImageToBase64('./assets/smartschool-project.jpg') ?>" alt="Logo Sekolah" width="80">
                    <?php else: ?>
                        <img src="<?= convertImageToBase64('./uploads/logo/' . $school->logo_institution) ?>" alt="Logo Lembaga Pendidikan" width="80">
                    <?php endif; ?>
                </td>
            </tr>
        </table>
    </div>

    <div style="width: 100%; margin-bottom: 20px;">
        <div style="text-align: center;">
            <?php if (!empty($student) && isset($class)) : ?>
                <h2 style="font-size: 14px; color: #000A12;">LAPORAN PRESTASI SISWA <?= strtoupper($student->name)  ?> BULAN <?= strtoupper(bulan_indo(date('F', strtotime($month))) . ' ' . date('Y', strtotime($month))) ?></h2>
            <?php else: ?>
                <h2 style="font-size: 14px; color: #000A12;">LAPORAN PRESTASI SELURUH SISWA <?= strtoupper($class->name) ?> BULAN <?= strtoupper(bulan_indo(date('F', strtotime($month))) . ' ' . date('Y', strtotime($month))) ?></h2>
            <?php endif; ?>
        </div>

        <table class="table table-bordered">
            <?php if (!empty($student)): ?>
                <!-- LAPORAN PER SISWA -->
                <thead>
                    <tr>
                        <th width="8%">NO</th>
                        <th width="20%">TANGGAL</th>
                        <th width="62%">DESKRIPSI</th>
                        <th width="10%">POIN</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($achievement)): ?>
                        <?php foreach ($achievement as $key => $value): ?>
                            <tr>
                                <td width="8%"><?= $key + 1 ?></td>
                                <td width="20%"><?= tgl_indo($value->date) ?></td>
                                <td width="62%"><?= nl2br(wordwrap($value->description, 80, "\n", true)) ?></td>
                                <td width="10%"><?= $value->achievementpoint ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="4" style="text-align: center;">Tidak ada laporan prestasi siswa</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            <?php else: ?>
                <!-- LAPORAN PER KELAS -->
                <thead>
                    <tr>
                        <th width="8%">NO</th>
                        <th width="12%">NIS</th>
                        <th width="20%">NAMA SISWA</th>
                        <th width="18%">TANGGAL</th>
                        <th width="32%">DESKRIPSI</th>
                        <th width="10%">POIN</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($achievement)): ?>
                        <?php foreach ($achievement as $key => $value): ?>
                            <tr>
                                <td width="8%"><?= $key + 1 ?></td>
                                <td width="12%"><?= $value->nis ?? '-' ?></td>
                                <td width="20%" class="nama-pesertadidik"><?= strtoupper($value->studentname) ?></td>
                                <td width="18%"><?= tgl_indo($value->date) ?></td>
                                <td width="32%"><?= nl2br(wordwrap($value->description, 60, "\n", true)) ?></td>
                                <td width="10%"><?= $value->achievementpoint ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" style="text-align: center;">Tidak ada laporan prestasi siswa</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            <?php endif; ?>
        </table>
    </div>
</body>

</html>