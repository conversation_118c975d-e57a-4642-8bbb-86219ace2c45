<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsQuestionmaterial extends MY_Model
{
    protected $table = 'msquestionmaterial';

    public $SearchDatatables = array(
        "a.name",
        "b.name"
    );

    public function QueryDatatables()
    {
        $this->db->select('a.id,a.name,b.name as coursename')
            ->from($this->table . ' a')
            ->join('mscourse b', 'a.courseid = b.id')
            ->order_by('a.name', 'ASC');

        return $this;
    }
}
