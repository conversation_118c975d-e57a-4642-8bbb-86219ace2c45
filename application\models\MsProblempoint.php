<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsProblempoint extends MY_Model
{
    protected $table = 'msproblempoint';

    public $SearchDatatables = array(
        "a.name",
        "a.point"
    );

    public function QueryDatatables()
    {
        $this->db->select('a.*')
            ->from($this->table . ' a')
            ->order_by('a.name', 'ASC');

        return $this;
    }
}
