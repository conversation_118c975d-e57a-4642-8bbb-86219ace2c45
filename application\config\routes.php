<?php
defined('BASEPATH') or exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'DashboardadminController';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

$route['change-semester'] = 'ChangeSemesterController';

$route['landing'] = 'AuthadminController/landing';
$route['privacy-policy'] = 'Welcome/privacy_policy';

//Dashboard
$route['dashboard/admin'] = 'DashboardadminController';
$route['dashboard/admin/datatables/amountstudent'] = 'DashboardadminController/datatables_amountstudent';
$route['dashboard/admin/datatables/notattendancestudent'] = 'DashboardadminController/datatables_notattendancestudent';
$route['dashboard/admin/datatables/notattendanceteacher'] = 'DashboardadminController/datatables_notattendanceteacher';

$route['datatables/amountstudent'] = 'DashboardadminController/datatables_amountstudent';
$route['datatables/notattendancestudent'] = 'DashboardadminController/datatables_notattendancestudent';
$route['datatables/notattendanceteacher'] = 'DashboardadminController/datatables_notattendanceteacher';

$route['dashboard/teacher'] = 'DashboardteacherController';
$route['dashboard/teacher/datatables/studentattendance'] = 'DashboardteacherController/datatables_studentattendance';
$route['dashboard/teacher/datatables/permission'] = 'DashboardteacherController/datatables_permission';

$route['datatables/studentattendance'] = 'DashboardteacherController/datatables_studentattendance';
$route['datatables/permission'] = 'DashboardteacherController/datatables_permission';

//Auth
$route['auth/login/admin'] = 'AuthadminController/login';
$route['auth/login/admin/process'] = 'AuthadminController/process_login';
$route['auth/changepassword/admin'] = 'AuthadminController/change_password';
$route['auth/changepassword/admin/process'] = 'AuthadminController/process_change_password';
$route['auth/logout/admin'] = 'AuthadminController/logout';

$route['auth/login/teacher'] = 'AuthteacherController/login';
$route['auth/login/teacher/process'] = 'AuthteacherController/process_login';
$route['auth/changepassword/teacher'] = 'AuthteacherController/change_password';
$route['auth/changepassword/teacher/process'] = 'AuthteacherController/process_change_password';
$route['auth/logout/teacher'] = 'AuthteacherController/logout';

$route['auth/login/student'] = 'AuthstudentController/login';
$route['auth/login/student/process'] = 'AuthstudentController/process_login';
$route['auth/changepassword/student'] = 'AuthstudentController/change_password';
$route['auth/changepassword/student/process'] = 'AuthstudentController/process_change_password';
$route['auth/logout/student'] = 'AuthstudentController/logout';

//Master
$route['master/categorynews'] = 'NewsCategoryController';
$route['master/categorynews/datatables'] = 'NewsCategoryController/datatables';
$route['master/categorynews/add'] = 'NewsCategoryController/add';
$route['master/categorynews/add/process'] = 'NewsCategoryController/process_add';
$route['master/categorynews/edit/(:num)'] = 'NewsCategoryController/edit/$1';
$route['master/categorynews/edit/(:num)/process'] = 'NewsCategoryController/process_edit/$1';
$route['master/categorynews/delete'] = 'NewsCategoryController/process_delete';

$route['master/news'] = 'NewsController';
$route['master/news/datatables'] = 'NewsController/datatables';
$route['master/news/add'] = 'NewsController/add';
$route['master/news/add/process'] = 'NewsController/process_add';
$route['master/news/edit/(:num)'] = 'NewsController/edit/$1';
$route['master/news/edit/(:num)/process'] = 'NewsController/process_edit/$1';
$route['master/news/delete'] = 'NewsController/process_delete';

$route['master/slider'] = 'SliderController';
$route['master/slider/datatables'] = 'SliderController/datatables';
$route['master/slider/add'] = 'SliderController/add';
$route['master/slider/add/process'] = 'SliderController/process_add';
$route['master/slider/edit/(:num)'] = 'SliderController/edit/$1';
$route['master/slider/edit/(:num)/process'] = 'SliderController/process_edit/$1';
$route['master/slider/delete'] = 'SliderController/process_delete';

$route['master/ads'] = 'AdsController';
$route['master/ads/datatables'] = 'AdsController/datatables';
$route['master/ads/add'] = 'AdsController/add';
$route['master/ads/add/process'] = 'AdsController/process_add';
$route['master/ads/edit/(:num)'] = 'AdsController/edit/$1';
$route['master/ads/edit/(:num)/process'] = 'AdsController/process_edit/$1';
$route['master/ads/delete'] = 'AdsController/process_delete';
$route['master/ads/ispublish'] = 'AdsController/process_change_ispublish';

$route['master/categoryarchive'] = 'ArchiveCategoryController';
$route['master/categoryarchive/datatables'] = 'ArchiveCategoryController/datatables';
$route['master/categoryarchive/add'] = 'ArchiveCategoryController/add';
$route['master/categoryarchive/add/process'] = 'ArchiveCategoryController/process_add';
$route['master/categoryarchive/edit/(:num)'] = 'ArchiveCategoryController/edit/$1';
$route['master/categoryarchive/edit/(:num)/process'] = 'ArchiveCategoryController/process_edit/$1';
$route['master/categoryarchive/delete'] = 'ArchiveCategoryController/process_delete';

$route['master/archive'] = 'ArchiveController';
$route['master/archive/datatables'] = 'ArchiveController/datatables';
$route['master/archive/add'] = 'ArchiveController/add';
$route['master/archive/add/process'] = 'ArchiveController/process_add';
$route['master/archive/edit/(:num)'] = 'ArchiveController/edit/$1';
$route['master/archive/edit/(:num)/process'] = 'ArchiveController/process_edit/$1';
$route['master/archive/delete'] = 'ArchiveController/process_delete';

$route['master/gallery'] = 'GalleryController';
$route['master/gallery/datatables'] = 'GalleryController/datatables';
$route['master/gallery/add'] = 'GalleryController/add';
$route['master/gallery/add/process_add'] = 'GalleryController/process_add';
$route['master/gallery/edit/(:num)'] = 'GalleryController/edit/$1';
$route['master/gallery/edit/(:num)/process'] = 'GalleryController/process_edit/$1';
$route['master/gallery/delete'] = 'GalleryController/delete';

$route['master/activity'] = 'ActivityController';
$route['master/activity/datatables'] = 'ActivityController/datatables';
$route['master/activity/add'] = 'ActivityController/add';
$route['master/activity/add/process'] = 'ActivityController/process_add';
$route['master/activity/edit/(:num)'] = 'ActivityController/edit/$1';
$route['master/activity/edit/(:num)/process'] = 'ActivityController/process_edit/$1';
$route['master/activity/delete'] = 'ActivityController/delete';

$route['master/typeactivity'] = 'TypeactivityController';
$route['master/typeactivity/datatables'] = 'TypeactivityController/datatables';
$route['master/typeactivity/add'] = 'TypeactivityController/add';
$route['master/typeactivity/add/process'] = 'TypeactivityController/process_add';
$route['master/typeactivity/edit/(:num)'] = "TypeactivityController/edit/$1";
$route['master/typeactivity/edit/(:num)/process'] = 'TypeactivityController/process_edit/$1';
$route['master/typeactivity/delete'] = 'TypeactivityController/delete';

$route['master/learningmaterial'] = 'LearningMaterialController';
$route['master/learningmaterial/datatables'] = 'LearningMaterialController/datatables';
$route['master/learningmaterial/add'] = 'LearningMaterialController/add';
$route['master/learningmaterial/add/process'] = 'LearningMaterialController/process_add';
$route['master/learningmaterial/edit/(:num)'] = 'LearningMaterialController/edit/$1';
$route['master/learningmaterial/edit/(:num)/process'] = 'LearningMaterialController/process_edit/$1';
$route['master/learningmaterial/delete'] = 'LearningMaterialController/process_delete';


$route['master/academicyear'] = 'AcademicyearController';
$route['master/academicyear/datatables'] = 'AcademicyearController/datatables';
$route['master/academicyear/add'] = 'AcademicyearController/add';
$route['master/academicyear/add/process'] = 'AcademicyearController/process_add';
$route['master/academicyear/edit/(:num)'] = 'AcademicyearController/edit/$1';
$route['master/academicyear/edit/(:num)/process'] = 'AcademicyearController/process_edit/$1';
$route['master/academicyear/delete'] = 'AcademicyearController/process_delete';
$route['master/academicyear/isactive'] = 'AcademicyearController/process_change_isactive';
$route['master/academicyear/changeall'] = 'AcademicyearController/process_change_all';

$route['master/class'] = 'ClassController';
$route['master/class/datatables'] = 'ClassController/datatables';
$route['master/class/add'] = 'ClassController/add';
$route['master/class/add/process'] = 'ClassController/process_add';
$route['master/class/edit/(:num)'] = 'ClassController/edit/$1';
$route['master/class/edit/(:num)/process'] = 'ClassController/process_edit/$1';
$route['master/class/delete'] = 'ClassController/process_delete';

$route['master/student'] = 'StudentController';
$route['master/student/datatables'] = 'StudentController/datatables';
$route['master/student/add'] = 'StudentController/add';
$route['master/student/add/process'] = 'StudentController/process_add';
$route['master/student/edit/(:num)'] = 'StudentController/edit/$1';
$route['master/student/edit/(:num)/process'] = 'StudentController/process_edit/$1';
$route['master/student/delete'] = 'StudentController/process_delete';
$route['master/student/formatexcel'] = 'StudentController/format_excel';
$route['master/student/exportexcel'] = 'StudentController/export_excel';
$route['master/student/pdf'] = 'StudentController/export_pdf';
$route['master/student/importexcel'] = 'StudentController/import_excel';
$route['master/student/importexcel/process'] = 'StudentController/process_import_excel';
$route['master/student/registercard'] = 'StudentController/registercard';
$route['master/student/registercard/process'] = 'StudentController/process_registercard';
$route['master/student/registercard/history'] = 'StudentController/history_registercard';
$route['master/student/registerrfid'] = 'StudentController/registerrfid';
$route['master/student/registerrfid/process'] = 'StudentController/process_registerrfid';
$route['master/student/unregisterrfid'] = 'StudentController/unregisterrfid';

$route['master/schedule/holiday'] = 'ScheduleholidayController';
$route['master/schedule/holiday/datatables'] = 'ScheduleholidayController/datatables';
$route['master/schedule/holiday/add'] = 'ScheduleholidayController/add';
$route['master/schedule/holiday/add/process'] = 'ScheduleholidayController/process_add';
$route['master/schedule/holiday/edit/(:num)'] = 'ScheduleholidayController/edit/$1';
$route['master/schedule/holiday/edit/(:num)/process'] = 'ScheduleholidayController/process_edit/$1';
$route['master/schedule/holiday/delete'] = 'ScheduleholidayController/process_delete';

$route['master/schedule/class/datatables'] = 'ScheduleclassController/datatables';
$route['master/schedule/class/delete'] = 'ScheduleclassController/process_delete';
$route['master/schedule/class/teacher'] = 'ScheduleclassController/getTeacher';
$route['master/schedule/class/(:any)'] = 'ScheduleclassController/index/$1';
$route['master/schedule/class/(:any)/pdf'] = 'ScheduleclassController/export_pdf/$1';
$route['master/schedule/class/(:any)/add'] = 'ScheduleclassController/add/$1';
$route['master/schedule/class/(:any)/add/process'] = 'ScheduleclassController/process_add/$1';
$route['master/schedule/class/(:any)/edit/(:num)'] = 'ScheduleclassController/edit/$1/$2';
$route['master/schedule/class/(:any)/edit/(:num)/process'] = 'ScheduleclassController/process_edit/$1/$2';

$route['master/homework'] = 'HomeworkController/student/$1';
$route['master/homework/detail/(:any)'] = 'HomeworkController/detail/$1';
$route['master/homework/detail/(:any)/datatables'] = 'HomeworkController/datatables_homework/$1';
$route['master/homework/detail/(:any)/add'] = 'HomeworkController/add_homework/$1';
$route['master/homework/detail/(:any)/add/process'] = 'HomeworkController/process_add_homework/$1';
$route['master/homework/detail/(:any)/edit'] = 'HomeworkController/edit_homework/$1';
$route['master/homework/detail/(:any)/edit/process'] = 'HomeworkController/process_edit_homework/$1';
$route['master/homework/detail/(:any)/delete'] = 'HomeworkController/process_delete_homework/$1';

$route['master/homework/evaluation/(:num)'] = 'HomeworkController/evaluation/$1';
$route['master/homework/evaluation/(:num)/content'] = 'HomeworkController/evaluation_content/$1';
$route['master/homework/evaluation/(:num)/process'] = 'HomeworkController/process_evaluation/$1';

$route['master/permission'] = 'PermissionstudentController';
$route['master/permission/student/datatables'] = 'PermissionstudentController/datatables';
$route['master/permission/student/add'] = 'PermissionstudentController/add';
$route['master/permission/student/add/process'] = 'PermissionstudentController/process_add';
$route['master/permission/student/edit/(:num)'] = 'PermissionstudentController/edit/$1';
$route['master/permission/student/edit/(:num)/process'] = 'PermissionstudentController/process_edit/$1';
$route['master/permission/student/delete'] = 'PermissionstudentController/process_delete';
$route['master/permission/student/getstudent'] = 'PermissionstudentController/getstudent';

$route['master/permission/teacher/datatables'] = 'PermissionteacherController/datatables';
$route['master/permission/teacher/add'] = 'PermissionteacherController/add';
$route['master/permission/teacher/add/process'] = 'PermissionteacherController/process_add';
$route['master/permission/teacher/edit/(:num)'] = 'PermissionteacherController/edit/$1';
$route['master/permission/teacher/edit/(:num)/process'] = 'PermissionteacherController/process_edit/$1';
$route['master/permission/teacher/delete'] = 'PermissionteacherController/process_delete';
$route['master/permission/teacher/getstudent'] = 'PermissionteacherController/getstudent';

$route['master/teacher'] = 'TeacherController';
$route['master/teacher/datatables'] = 'TeacherController/datatables';
$route['master/teacher/add'] = 'TeacherController/add';
$route['master/teacher/add/process'] = 'TeacherController/process_add';
$route['master/teacher/edit/(:num)'] = 'TeacherController/edit/$1';
$route['master/teacher/edit/(:num)/process'] = 'TeacherController/process_edit/$1';
$route['master/teacher/delete'] = 'TeacherController/process_delete';
$route['master/teacher/registercard'] = 'TeacherController/registercard';
$route['master/teacher/registercard/process'] = 'TeacherController/process_registercard';
$route['master/teacher/registercard/history'] = 'TeacherController/history_registercard';
$route['master/teacher/registerrfid'] = 'TeacherController/registerrfid';
$route['master/teacher/registerrfid/process'] = 'TeacherController/process_registerrfid';
$route['master/teacher/unregisterrfid'] = 'TeacherController/unregisterrfid';

$route['master/position'] = 'PositionController';
$route['master/position/datatables'] = 'PositionController/datatables';
$route['master/position/add'] = 'PositionController/add';
$route['master/position/add/process'] = 'PositionController/process_add';
$route['master/position/edit/(:num)'] = 'PositionController/edit/$1';
$route['master/position/edit/(:num)/process'] = 'PositionController/process_edit/$1';
$route['master/position/delete'] = 'PositionController/process_delete';

$route['master/course'] = 'CourseController';
$route['master/course/datatables'] = 'CourseController/datatables';
$route['master/course/add'] = 'CourseController/add';
$route['master/course/add/process'] = 'CourseController/process_add';
$route['master/course/edit/(:num)'] = 'CourseController/edit/$1';
$route['master/course/edit/(:num)/process'] = 'CourseController/process_edit/$1';
$route['master/course/delete'] = 'CourseController/process_delete';

$route['master/promotionclass'] = 'PromotionclassController';
$route['master/promotionclass/datatables'] = 'PromotionclassController/datatables';
$route['master/promotionclass/student/(:num)'] = 'PromotionclassController/student/$1';
$route['master/promotionclass/student/(:num)/process'] = 'PromotionclassController/process_student/$1';

$route['master/graduation'] = 'GraduationController';
$route['master/graduation/datatables'] = 'GraduationController/datatables';
$route['master/graduation/add'] = 'GraduationController/add';
$route['master/graduation/add/process'] = 'GraduationController/process_add';
$route['master/graduation/edit/(:any)'] = 'GraduationController/edit/$1';
$route['master/graduation/edit/(:any)/process'] = 'GraduationController/process_edit/$1';
$route['master/graduation/delete'] = 'GraduationController/process_delete';
$route['master/graduation/student'] = 'GraduationController/student';
$route['master/graduation/key'] = 'GraduationController/process_key';
$route['master/graduation/detail/(:any)'] = 'GraduationController/detail/$1';

$route['master/sitemap'] = 'SitemapController';
$route['master/sitemap/datatables'] = 'SitemapController/datatables';
$route['master/sitemap/add'] = 'SitemapController/add';
$route['master/sitemap/add/process'] = 'SitemapController/process_add';
$route['master/sitemap/edit/(:num)'] = 'SitemapController/edit/$1';
$route['master/sitemap/edit/(:num)/process'] = 'SitemapController/process_edit/$1';
$route['master/sitemap/delete'] = 'SitemapController/process_delete';

$route['master/homework'] = 'HomeworkController';
$route['master/homework/datatables'] = 'HomeworkController/datatables';

$route['master/announcement'] = 'AnnouncementController';
$route['master/announcement/datatables'] = 'AnnouncementController/datatables';
$route['master/announcement/add'] = 'AnnouncementController/add';
$route['master/announcement/add/process'] = 'AnnouncementController/process_add';
$route['master/announcement/edit/(:num)'] = 'AnnouncementController/edit/$1';
$route['master/announcement/edit/(:num)/process'] = 'AnnouncementController/process_edit/$1';
$route['master/announcement/delete'] = 'AnnouncementController/process_delete';

//Counseling
$route['counseling/data'] = 'CounselingController';
$route['counseling/data/datatables'] = 'CounselingController/datatables';
$route['counseling/data/detail/(:num)'] = 'CounselingController/detail/$1';
$route['counseling/data/detail/(:num)/achievement/datatables'] = 'CounselingController/datatables_achievement/$1';
$route['counseling/data/detail/(:num)/achievement/add'] = 'CounselingController/add_achievement/$1';
$route['counseling/data/detail/(:num)/achievement/add/process'] = 'CounselingController/process_add_achievement/$1';
$route['counseling/data/detail/(:num)/achievement/edit/(:num)'] = 'CounselingController/edit_achievement/$1/$2';
$route['counseling/data/detail/(:num)/achievement/edit/(:num)/process'] = 'CounselingController/process_edit_achievement/$1/$2';
$route['counseling/data/detail/(:num)/achievement/delete'] = 'CounselingController/process_delete_achievement/$1';
$route['counseling/data/detail/(:num)/problem/datatables'] = 'CounselingController/datatables_problem/$1';
$route['counseling/data/detail/(:num)/problem/add'] = 'CounselingController/add_problem/$1';
$route['counseling/data/detail/(:num)/problem/add/process'] = 'CounselingController/process_add_problem/$1';
$route['counseling/data/detail/(:num)/problem/edit/(:num)'] = 'CounselingController/edit_problem/$1/$2';
$route['counseling/data/detail/(:num)/problem/edit/(:num)/process'] = 'CounselingController/process_edit_problem/$1/$2';
$route['counseling/data/detail/(:num)/problem/delete'] = 'CounselingController/process_delete_problem/$1';
$route['counseling/data/getproblem'] = 'CounselingController/getProblem';
$route['counseling/data/getachievement'] = 'CounselingController/getAchievement';

$route['counseling/settingspoint'] = 'SettingspointController';
$route['counseling/settingspoint/achievement/datatables'] = 'SettingspointController/datatables_achievement';
$route['counseling/settingspoint/problem/datatables'] = 'SettingspointController/datatables_problem';
$route['counseling/settingspoint/achievement/add'] = 'SettingspointController/add_achievement';
$route['counseling/settingspoint/achievement/add/process'] = 'SettingspointController/process_add_achievement';
$route['counseling/settingspoint/achievement/edit/(:num)'] = 'SettingspointController/edit_achievement/$1';
$route['counseling/settingspoint/achievement/edit/(:num)/process'] = 'SettingspointController/process_edit_achievement/$1';
$route['counseling/settingspoint/achievement/delete'] = 'SettingspointController/process_delete_achievement';
$route['counseling/settingspoint/problem/add'] = 'SettingspointController/add_problem';
$route['counseling/settingspoint/problem/add/process'] = 'SettingspointController/process_add_problem';
$route['counseling/settingspoint/problem/edit/(:num)'] = 'SettingspointController/edit_problem/$1';
$route['counseling/settingspoint/problem/edit/(:num)/process'] = 'SettingspointController/process_edit_problem/$1';
$route['counseling/settingspoint/problem/delete'] = 'SettingspointController/process_delete_problem';

//Settings
$route['settings/holiday'] = 'SettingsController/holiday';
$route['settings/holiday/process'] = 'SettingsController/process_holiday';

$route['settings/attendance'] = 'SettingsController/Attendance';
$route['settings/attendance/datatables'] = 'SettingsController/datatables_attendance';
$route['settings/attendance/process'] = 'SettingsController/process_attendance';
$route['settings/attendance/type'] = 'SettingsController/type_attendance';
$route['settings/attendance/add'] = 'SettingsController/add_attendance';
$route['settings/attendance/add/process'] = 'SettingsController/process_add_attendance';
$route['settings/attendance/edit/(:num)'] = 'SettingsController/edit_attendance/$1';
$route['settings/attendance/edit/(:num)/process'] = 'SettingsController/process_edit_attendance/$1';
$route['settings/attendance/delete'] = 'SettingsController/process_delete_attendance';

$route['settings/school'] = 'SettingsController/school';
$route['settings/school/process'] = 'SettingsController/process_school';

$route['settings/principal'] = 'PrincipalController';
$route['settings/principal/process'] = 'PrincipalController/process';

$route['settings/visionmission'] = 'SettingsController/visionmission';
$route['settings/visionmission/process'] = 'SettingsController/process_visionmission';

// PPDB
$route['ppdb/path'] = 'PpdbPathController';
$route['ppdb/path/datatables'] = 'PpdbPathController/datatables';
$route['ppdb/path/add'] = 'PpdbPathController/add';
$route['ppdb/path/add/process'] = 'PpdbPathController/process_add';
$route['ppdb/path/edit/(:num)'] = 'PpdbPathController/edit/$1';
$route['ppdb/path/edit/(:num)/process'] = 'PpdbPathController/process_edit/$1';
$route['ppdb/path/delete'] = 'PpdbPathController/process_delete';

$route['ppdb/register'] = 'PpdbController';
$route['ppdb/register/datatables'] = 'PpdbController/datatables';
$route['ppdb/register/add'] = 'PpdbController/add';
$route['ppdb/register/add/process'] = 'PpdbController/process_add';
$route['ppdb/register/edit/(:num)'] = 'PpdbController/edit/$1';
$route['ppdb/register/edit/(:num)/process'] = 'PpdbController/process_edit/$1';
$route['ppdb/register/delete'] = 'PpdbController/process_delete';

//Attendance
$route['attendance'] = 'AttendanceController/index';
$route['attendance/process'] = 'AttendanceController/process_attendance';
$route['attendance/history'] = 'AttendanceController/history';

$route['attendancecourse'] = 'AttendancecourseController/index';
$route['attendancecourse/datatables'] = 'AttendancecourseController/datatables';
$route['attendancecourse/student/(:num)'] = 'AttendancecourseController/student/$1';
$route['attendancecourse/student/(:num)/process'] = 'AttendancecourseController/process_attendance_student/$1';
$route['attendancecourse/student/(:num)/exportexcel'] = 'AttendancecourseController/export_excel/$1';
$route['attendancecourse/student/(:num)/exportpdf'] = 'AttendancecourseController/export_pdf/$1';

//Report Attendance
$route['report/attendance/student'] = 'ReportController/attendance_student';
$route['report/attendance/student/preview'] = 'ReportController/attendance_student_preview';
$route['report/attendance/student/pdf'] = 'ReportController/attendance_student_pdf';
$route['report/attendance/student/process'] = 'ReportController/process_attendance_student';

$route['report/attendance/teacher'] = 'ReportController/attendance_teacher';
$route['report/attendance/teacher/preview'] = 'ReportController/attendance_teacher_preview';
$route['report/attendance/teacher/pdf'] = 'ReportController/attendance_teacher_pdf';
$route['report/attendance/teacher/process'] = 'ReportController/process_attendance_teacher';

//Report Attendancecourse
$route['report/attendancecourse'] = 'ReportController/attendancecourse';
$route['report/attendancecourse/datatables'] = 'ReportController/attendancecourse_datatables';
$route['report/attendancecourse/process/(:num)'] = 'ReportController/process_attendancecourse/$1';
$route['report/attendancecourse/export/(:num)'] = 'ReportController/attendancecourse_export_pdf/$1';
$route['report/attendancecourse/student/(:num)'] = 'ReportController/attendancecourse_student/$1';

// Report Achievement
$route['report/achievement'] = 'ReportController/achievement';
$route['report/achievement/export'] = 'ReportController/export_achievement';
$route['report/achievement/preview'] = 'ReportController/achievement_student_preview';
$route['report/achievement/process'] = 'ReportController/process_achievement_student';

// Report Problem
$route['report/problem'] = 'ReportController/problem';
$route['report/problem/export'] = 'ReportController/export_problem';
$route['report/problem/preview'] = 'ReportController/problem_student_preview';
$route['report/problem/process'] = 'ReportController/process_problem_student';


//CBT
$route['cbt/schedule'] = 'SchedulecbtController';
$route['cbt/schedule/datatables'] = 'SchedulecbtController/datatables';
$route['cbt/schedule/add'] = 'SchedulecbtController/add';
$route['cbt/schedule/add/process'] = 'SchedulecbtController/process_add';
$route['cbt/schedule/edit/(:num)'] = 'SchedulecbtController/edit/$1';
$route['cbt/schedule/edit/(:num)/process'] = 'SchedulecbtController/process_edit/$1';
$route['cbt/schedule/delete'] = 'SchedulecbtController/process_delete';
$route['cbt/schedule/question/(:num)'] = 'SchedulecbtController/question/$1';
$route['cbt/schedule/question/(:num)/datatables'] = 'SchedulecbtController/datatables_question/$1';
$route['cbt/schedule/student/(:num)'] = 'SchedulecbtController/student/$1';
$route['cbt/schedule/student/(:num)/datatables'] = 'SchedulecbtController/datatables_student/$1';
$route['cbt/schedule/student/(:num)/reset'] = 'SchedulecbtController/reset/$1';
$route['cbt/schedule/student/(:num)/exportexcel'] = 'SchedulecbtController/export_excel/$1';
$route['cbt/schedule/student/(:num)/exportpdf'] = 'SchedulecbtController/export_pdf/$1';
$route['cbt/schedule/student/previewprogress/(:any)'] = 'SchedulecbtController/preview_progress_question/$1';
$route['cbt/schedule/student/previewprogress/(:any)/content'] = 'SchedulecbtController/preview_progress_question_content/$1';
$route['cbt/schedule/getquestion'] = 'SchedulecbtController/getQuestion';
$route['cbt/schedule/getclass'] = 'SchedulecbtController/getClass';

$route['cbt/question'] = 'QuestionController';
$route['cbt/question/datatables'] = 'QuestionController/datatables';
$route['cbt/question/add'] = 'QuestionController/add';
$route['cbt/question/add/process'] = 'QuestionController/process_add';
$route['cbt/question/edit/(:num)'] = 'QuestionController/edit/$1';
$route['cbt/question/edit/(:num)/process'] = 'QuestionController/process_edit/$1';
$route['cbt/question/delete'] = 'QuestionController/process_delete';
$route['cbt/question/detail/(:num)'] = 'QuestionController/detail/$1';
$route['cbt/question/detail/(:num)/process'] = 'QuestionController/process_detail/$1';
$route['cbt/question/detail/(:num)/edit'] = 'QuestionController/edit_detail/$1';
$route['cbt/question/detail/(:num)/edit/process'] = 'QuestionController/process_edit_detail/$1';
$route['cbt/question/detail/(:num)/delete'] = 'QuestionController/process_delete_detail/$1';
$route['cbt/question/detail/(:num)/getquestion'] = 'QuestionController/getQuestion/$1';

//CBT STUDENT
$route['cbt/student'] = 'CbtController';
$route['cbt/student/token'] = 'CbtController/token';
$route['cbt/student/token/process'] = 'CbtController/process_token';
$route['cbt/student/question/(:any)'] = 'CbtController/question/$1';
$route['cbt/student/question/(:any)/process'] = 'CbtController/process_question/$1';
$route['cbt/student/question/(:any)/content'] = 'CbtController/question_content/$1';
$route['cbt/student/question/(:any)/answer'] = 'CbtController/process_answer/$1';
$route['cbt/student/question/(:any)/finish'] = 'CbtController/process_finish/$1';

// SISTEM PEMBAYARAN (SPP)
$route['payment/category'] = 'PaymentcategoryController';
$route['payment/category/datatables'] = 'PaymentcategoryController/datatables';
$route['payment/category/add'] = 'PaymentcategoryController/add';
$route['payment/category/add/process'] = 'PaymentcategoryController/process_add';
$route['payment/category/edit/(:num)'] = 'PaymentcategoryController/edit/$1';
$route['payment/category/edit/(:num)/process'] = 'PaymentcategoryController/process_edit/$1';
$route['payment/category/delete'] = 'PaymentcategoryController/process_delete';

$route['payment/invoice'] = 'PaymentinvoiceController';
$route['payment/invoice/datatables'] = 'PaymentinvoiceController/datatables';
$route['payment/invoice/broadcast'] = 'PaymentinvoiceController/broadcast';
$route['payment/invoice/broadcast/process'] = 'PaymentinvoiceController/process_broadcast';
$route['payment/invoice/(:num)'] = 'PaymentinvoiceController/detail/$1';
$route['payment/invoice/(:num)/datatables'] = 'PaymentinvoiceController/datatables_detail/$1';
$route['payment/invoice/(:num)/add'] = 'PaymentinvoiceController/add/$1';
$route['payment/invoice/(:num)/add/process'] = 'PaymentinvoiceController/process_add/$1';
$route['payment/invoice/(:num)/cancel'] = 'PaymentinvoiceController/cancel/$1';
$route['payment/invoice/(:num)/pay'] = 'PaymentinvoiceController/pay/$1';
$route['payment/invoice/(:num)/pay/process'] = 'PaymentinvoiceController/process_pay/$1';

//Select
$route['select/city'] = 'SelectController/city';
$route['select/district'] = 'SelectController/district';
$route['select/subdistrict'] = 'SelectController/subdistrict';
$route['select/student'] = 'SelectController/student';

//Recap
$route['recap/attendance/student'] = 'RecapattendanceController/student';
$route['recap/attendance/student/datatables'] = 'RecapattendanceController/datatables_student';

$route['recap/attendance/teacher'] = 'RecapattendanceController/teacher';
$route['recap/attendance/teacher/datatables'] = 'RecapattendanceController/datatables_teacher';

//API Settings
$route['api/settings/logo'] = 'API/Settings/logo';

// API Teacher
$route['api/teacher/dashboard/info'] = 'API/Teacher/Dashboard/info';

$route['api/teacher/auth/login'] = 'API/Teacher/Auth/login';
$route['api/teacher/auth/login/resend'] = 'API/Teacher/Auth/resend';
$route['api/teacher/auth/login/verify'] = 'API/Teacher/Auth/verify';
$route['api/teacher/auth/login/password'] = 'API/Teacher/Auth/password';

$route['api/teacher/account/change/password'] = 'API/Teacher/Account/change_password';
$route['api/teacher/account/fcm-token'] = 'API/Teacher/Account/fcm_token';
$route['api/teacher/account/notification'] = 'API/Teacher/Account/notification';

$route['api/teacher/absence/today'] = 'API/Teacher/Absence/today';
$route['api/teacher/absence/history'] = 'API/Teacher/Absence/history';

$route['api/teacher/counseling/problem'] = 'API/Teacher/Counseling/problem';
$route['api/teacher/counseling/problem/add'] = 'API/Teacher/Counseling/add_problem';
$route['api/teacher/counseling/problem/edit'] = 'API/Teacher/Counseling/edit_problem';
$route['api/teacher/counseling/problem/delete'] = 'API/Teacher/Counseling/delete_problem';
$route['api/teacher/counseling/achievement'] = 'API/Teacher/Counseling/achievement';
$route['api/teacher/counseling/achievement/add'] = 'API/Teacher/Counseling/add_achievement';
$route['api/teacher/counseling/achievement/edit'] = 'API/Teacher/Counseling/edit_achievement';
$route['api/teacher/counseling/achievement/delete'] = 'API/Teacher/Counseling/delete_achievement';

$route['api/teacher/data/class'] = 'API/Teacher/Data/class_';
$route['api/teacher/data/student'] = 'API/Teacher/Data/student';
$route['api/teacher/data/achievementpoint'] = 'API/Teacher/Data/achievementpoint';
$route['api/teacher/data/problempoint'] = 'API/Teacher/Data/problempoint';
$route['api/teacher/data/course'] = 'API/Teacher/Data/course';
$route['api/teacher/data/courseclass'] = 'API/Teacher/Data/courseclass';

$route['api/teacher/scheduleclass/today'] = 'API/Teacher/Scheduleclass/today';
$route['api/teacher/scheduleclass/student'] = 'API/Teacher/Scheduleclass/student';
$route['api/teacher/scheduleclass/attendance'] = 'API/Teacher/Scheduleclass/attendance';
$route['api/teacher/scheduleclass/all'] = 'API/Teacher/Scheduleclass/all';

$route['api/teacher/scheduleholiday'] = 'API/Teacher/Scheduleholiday';

$route['api/teacher/homework'] = 'API/Teacher/Homework';
$route['api/teacher/homework/add'] = 'API/Teacher/Homework/add';
$route['api/teacher/homework/delete'] = 'API/Teacher/Homework/delete';
$route['api/teacher/homework/edit'] = 'API/Teacher/Homework/edit';
$route['api/teacher/homework/detail'] = 'API/Teacher/Homework/detail';
$route['api/teacher/homework/collect'] = 'API/Teacher/Homework/collect';

//API Parent
$route['api/parent/dashboard/info'] = 'API/Parent/Dashboard/info';

$route['api/parent/auth/login'] = 'API/Parent/Auth/login';
$route['api/parent/auth/login/resend'] = 'API/Parent/Auth/resend';
$route['api/parent/auth/login/verify'] = 'API/Parent/Auth/verify';
$route['api/parent/auth/login/password'] = 'API/Parent/Auth/password';

$route['api/parent/account/change/password'] = 'API/Parent/Account/change_password';
$route['api/parent/account/fcm-token'] = 'API/Parent/Account/fcm_token';
$route['api/parent/account/notification'] = 'API/Parent/Account/notification';

$route['api/parent/absence/today'] = 'API/Parent/Absence/today';
$route['api/parent/absence/history'] = 'API/Parent/Absence/history';

$route['api/parent/counseling/problem'] = 'API/Parent/Counseling/problem';
$route['api/parent/counseling/achievement'] = 'API/Parent/Counseling/achievement';

$route['api/parent/scheduleclass/today'] = 'API/Parent/Scheduleclass/today';
$route['api/parent/scheduleclass/all'] = 'API/Parent/Scheduleclass/all';

$route['api/parent/scheduleholiday'] = 'API/Parent/Scheduleholiday';

$route['api/parent/homework'] = 'API/Parent/Homework';

$route['api/parent/spp/invoice'] = 'API/Parent/Spp/invoice';

// Cronjobs
$route['cronjobs/attendance/whatsapp_notification'] = 'Cronjobs/whatsapp_notification';
$route['cronjobs/attendance/notification_teacher'] = 'Cronjobs/notification_teacher';
$route['cronjobs/cbt/finish_cbt'] = 'Cronjobs/finish_cbt';

//STUDENT PARENT
$route['dashboard/student'] = 'DashboardstudentController';

$route['scheduleholiday'] = 'StudentParent/ScheduleholidayController';
$route['scheduleholiday/datatables'] = 'StudentParent/ScheduleholidayController/datatables';

$route['scheduleclass'] = 'StudentParent/ScheduleclassController';

$route['permission'] = 'StudentParent/PermissionController';
$route['permission/datatables'] = 'StudentParent/PermissionController/datatables';

$route['problem'] = 'StudentParent/ProblemController';
$route['problem/datatables'] = 'StudentParent/ProblemController/datatables';

$route['achievement'] = 'StudentParent/AchievementController';
$route['achievement/datatables'] = 'StudentParent/AchievementController/datatables';

$route['cbt'] = 'StudentParent/CbtController';
$route['cbt/token'] = 'StudentParent/CbtController/token';
$route['cbt/token/process'] = 'StudentParent/CbtController/process_token';
$route['cbt/question/(:any)'] = 'StudentParent/CbtController/question/$1';
$route['cbt/question/(:any)/process'] = 'StudentParent/CbtController/process_question/$1';
$route['cbt/question/(:any)/content'] = 'StudentParent/CbtController/question_content/$1';
$route['cbt/question/(:any)/answer'] = 'StudentParent/CbtController/process_answer/$1';
$route['cbt/question/(:any)/finish'] = 'StudentParent/CbtController/process_finish/$1';

//HOMEWORK
$route['homework/student'] = 'HomeworkstudentController';
$route['homework/student/datatables'] = 'HomeworkstudentController/datatables';
$route['homework/student/detail'] = 'HomeworkstudentController/detail';
$route['homework/student/collect'] = 'HomeworkstudentController/collect';
$route['homework/student/collect/process'] = 'HomeworkstudentController/process_collect';

//LEARNINGMATERIAL
$route['learningmaterial/student'] = 'LearningStudentController';

//INBOX
$route['inbox'] = 'InboxController';
$route['inbox/datatables'] = 'InboxController/datatables';
$route['inbox/delete'] = 'InboxController/delete';

$route['callback/naiko'] = 'CallbackController/naiko';
