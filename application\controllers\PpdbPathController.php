<?php

use PhpOffice\PhpSpreadsheet\Calculation\MathTrig\Arabic;
use PhpOffice\PhpSpreadsheet\Writer\Ods\Thumbnails;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsPpdbpath $msppdbpath
 * @property TbPpdb $tbppdb
 * @property CI_DB_query_builder $db
 * @property Datatables $datatables
 */
class PpdbPathController extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->model('MsPpdbpath', 'msppdbpath');
        $this->load->model('TbPpdb', 'TbPpdb');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['title'] = 'Profil Sekolah - Jalur';
        $data['content'] = 'admin/ppdb/path/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable =  $this->datatables->make('MsPpdbpath', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getdata($where) as $key => $value) {
            $detail = array();
            $detail[] = $value->name;
            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('ppdb/path/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deletePath('" . $value->id .  "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $detail[] = $actions;
            $data[] = $detail;
        }
        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['title'] = 'Profil Sekolah - Tambah Jalur';
        $data['content'] = 'admin/ppdb/path/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $namaJ = trim(getPost('name'));

        if (empty($namaJ)) {
            return JSONResponseDefault('FAILED', 'Jalur tidak boleh kosong');
        }

        $cekJalur = $this->msppdbpath->get(array(
            'name' => $namaJ,
            'createdby' => getCurrentIdUser()
        ))->num_rows();

        if ($cekJalur > 0) {
            return JSONResponseDefault('FAILED', 'Jalur sudah ada');
        }

        $insert = array();
        $insert['name'] = $namaJ;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $insert = $this->msppdbpath->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Jalur berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Jalur gagal ditambahkan');
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $jalur = $this->msppdbpath->get(array('id' => $id));

        if ($jalur->num_rows() == 0) {
            return redirect('ppdb/path');
        }

        $data = array();
        $data['title'] = 'Profil Sekolah - Ubah Jalur';
        $data['content'] = 'admin/ppdb/path/edit';
        $data['path'] = $jalur->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $jalur = $this->msppdbpath->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($jalur->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $jalur = trim(getPost('name'));

        if (empty($jalur)) {
            return JSONResponseDefault('FAILED', 'Nama jalur tidak boleh kosong');
        }

        $cekJalur = $this->msppdbpath->get(array('name' => $jalur, 'id !=' => $id, 'createdby' => getCurrentIdUser()))->num_rows();

        if ($cekJalur > 0) {
            return JSONResponseDefault('FAILED', 'Jalur sudah ada');
        }

        $update = array();
        $update['name'] = $jalur;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $update = $this->msppdbpath->update(array('id' => $id), $update);

        if ($update) {
            return JSONResponseDefault('OK', 'Data jalur berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Data jalur diubah');
        }
    }

    public function process_delete()
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = getPost('id');

            if (empty($id)) {
                throw new Exception('Data tidak ditemukan');
            }

            // Cek apakah jalur ada
            $jalur = $this->msppdbpath->get(array('id' => $id));

            if ($jalur->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            // Ambil data peserta didik yang terkait dengan jalur yang akan dihapus
            $students = $this->TbPpdb->get(array('infopath' => $id))->result();

            foreach ($students as $student) {
                // Hapus file picture jika ada
                if (!empty($student->picture)) {
                    $picturePath = './uploads/studpic/' . $student->picture;
                    if (file_exists($picturePath)) {
                        unlink($picturePath);
                    }
                }

                // Hapus file certificate jika ada
                if (!empty($student->certificate)) {
                    $certificatePath = './uploads/certificate/' . $student->certificate;
                    if (file_exists($certificatePath)) {
                        unlink($certificatePath);
                    }
                }
            }

            // Hapus data peserta didik terkait jalur yang dihapus
            $this->TbPpdb->delete(array('infopath' => $id));

            // Hapus data jalur
            $this->msppdbpath->delete(array('id' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data jalur gagal dihapus');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data jalur dan peserta didik terkait berhasil dihapus');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }
}
