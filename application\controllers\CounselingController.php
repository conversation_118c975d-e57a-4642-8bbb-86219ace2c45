<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property TbProblem $tbproblem
 * @property MsProblempoint $msproblempoint
 * @property TbAchievement $tbachievement
 * @property MsAchievementpoint $msachievementpoint
 * @property MsStudent $msstudent
 * @property MsClass $msclass
 * @property CI_DB_query_builder $db
 * @property Datatables $datatables
 * @property CI_Upload $upload
 * @property TbNotifications $tbnotifications
 */
class CounselingController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('TbProblem', 'tbproblem');
        $this->load->model('MsProblempoint', 'msproblempoint');
        $this->load->model('TbAchievement', 'tbachievement');
        $this->load->model('MsAchievementpoint', 'msachievementpoint');
        $this->load->model('MsClass', 'msclass');
        $this->load->model('MsStudent', 'msstudent');
        $this->load->model('TbNotifications', 'tbnotifications');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Bimbingan Konseling';
        $data['content'] = 'admin/counseling/index';
        $data['class'] = $this->msclass->select('id,name,level')
            ->where(array(
                'createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool(),
                'isdeleted' => null
            ))
            ->order_by('roman_to_int(level),name', 'ASC')
            ->get()
            ->result();

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $classid = getPost('classid', null);

        $datatable = $this->datatables->make('MsStudent', 'QueryDatatables_counseling', 'SearchDatatables');

        $where = array();

        if (isAdmin()) {
            $where['a.createdby'] = getCurrentIdUser();
        } else if (isTeacher()) {
            $where['a.createdby'] = getCurrentIdSchool();
        }

        if ($classid != null) {
            $where['a.classid'] = $classid;
        }

        $where['a.status'] = 'Active';
        $where['a.isdeleted'] = null;

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();

            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('counseling/data/detail/' . $value->id) . "?tab=tab-achievement\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Detail\">
                    <i class=\"ti ti-eye\"></i>
                </a>
            </div>";

            $detail[] = $value->nis;
            $detail[] = $value->studentname;
            $detail[] = ($value->level ?? null) != null ? $value->level . ' - ' . $value->classname : $value->classname;
            $detail[] = $value->totalachievement ?? 0;
            $detail[] = $value->totalproblem ?? 0;
            $detail[] = $actions;


            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function detail($studentid)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cekstudent = $this->msstudent->select('a.*, b.level,b.name as classname, c.name as teachername')
            ->join('msclass b', 'b.id = a.classid')
            ->join('msteacher c', 'c.id = b.teacherid', 'left')
            ->where(array(
                'a.id' => $studentid,
                'a.status' => 'Active',
                'a.isdeleted' => null
            ))
            ->get();

        if ($cekstudent->num_rows() == 0) {
            return redirect(base_url('counseling/data'));
        }

        $data = array();
        $data['title'] = 'Bimbingan Konseling - Detail';
        $data['content'] = 'admin/counseling/detail';
        $data['student'] = $cekstudent->row();
        $data['totalachievement'] = $this->tbachievement->select('SUM(b.point) as total')
            ->join('msachievementpoint b', 'b.id = a.achievementpointid')
            ->get(array('a.studentid' => $studentid))->row()->total ?? 0;
        $data['totalproblem'] = $this->tbproblem->select('SUM(b.point) as total')
            ->join('msproblempoint b', 'b.id = a.problempointid')
            ->get(array('a.studentid' => $studentid))->row()->total ?? 0;

        return $this->load->view('master', $data);
    }

    public function datatables_achievement($studentid)
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('TbAchievement', 'QueryDatatables', 'SearchDatatables');

        $where = array();

        // $academicyear = getCurrentAcademicyear();
        // $academicyearstart = $academicyear->startdate ?? null;
        // $academicyearend = $academicyear->enddate ?? null;

        // if ($academicyearstart != null) {
        //     $where['a.date >='] = $academicyearstart;
        // }

        // if ($academicyearend != null) {
        //     $where['a.date <='] = $academicyearend;
        // }

        $where['b.status'] = 'Active';
        $where['a.studentid'] = $studentid;
        $where['b.isdeleted'] = null;

        $data = array();
        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();

            $text = $value->studentname . " pada tanggal " . tgl_indo($value->date);
            $actions = "";
            if (isAdmin()) {
                $actions .= "<div class=\"d-flex\">
                    <a href=\"" . base_url('counseling/data/detail/' . $studentid . '/achievement/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2 mb-1\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                        <i class=\"ti ti-edit\"></i>
                    </a>

                    <button type=\"button\" class=\"btn btn-danger btn-sm ms-2 mb-1\" onclick=\"deleteAchievement('" . $value->id . "','" . $text . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                        <i class=\"ti ti-trash\"></i>
                    </button>
                </div>";
            }

            if ($value->documenttype == 'application/pdf' && $value->document != null && file_exists('./uploads/achievement/' . $value->document)) {
                $actions .= "<div class=\"d-flex\">
                    <a href=\"" . base_url('uploads/achievement/' . $value->document) . "\" class=\"btn btn-danger btn-sm ms-2\" target=\"_blank\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"PDF\">
                        <i class=\"ti ti-file-text\"></i>
                    </a>
                </div>";
            } elseif ($value->documenttype != 'application/pdf' && $value->document != null  && file_exists('./uploads/achievement/' . $value->document)) {
                $actions .= "<div class=\"d-flex\">
                    <a href=\"" . base_url('uploads/achievement/' . $value->document) . "\" class=\"btn btn-success btn-sm ms-2\" target=\"_blank\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-success\" data-bs-original-title=\"Gambar\">
                        <i class=\"ti ti-photo\"></i>
                    </a>
                </div>";
            } else {
                if (!isAdmin()) {
                    $actions .= "N/A";
                }
            }

            $detail[] = tgl_indo($value->date);
            $detail[] = $value->achievementname ?? '-';
            $detail[] = $value->achievementpoint ?? 0;
            $detail[] = $value->description;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function add_achievement($studentid)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cekstudent = $this->msstudent->select('a.*, b.level,b.name as classname, c.name as teachername')
            ->join('msclass b', 'b.id = a.classid')
            ->join('msteacher c', 'c.id = b.teacherid', 'left')
            ->where(array(
                'a.id' => $studentid,
                'a.status' => 'Active',
                'a.isdeleted' => null
            ))
            ->get();

        if ($cekstudent->num_rows() == 0) {
            return redirect(base_url('counseling/data'));
        }

        $data = array();
        $data['title'] = 'Bimbingan Konseling - Tambah Prestasi Pesrta Didik';
        $data['content'] = 'admin/counseling/achievement/add';
        $data['student'] = $cekstudent->row();
        $data['achievement'] = $this->msachievementpoint->order_by('name', 'ASC')->get(array('createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->result();

        return $this->load->view('master', $data);
    }

    public function process_add_achievement($studentid)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $cekstudent = $this->msstudent->get(array(
                'id' => $studentid,
                'isdeleted' => null,
                'status' => 'Active'
            ));

            if ($cekstudent->num_rows() == 0) {
                throw new Exception('Peserta didik tidak ditemukan');
            }

            $rowstudent = $cekstudent->row();

            $achievementpointid = getPost('achievement');
            $date = getPost('date');
            $description = getPost('description');
            $document = $_FILES['document'];

            if ($achievementpointid == null) {
                throw new Exception('Prestasi tidak boleh kosong');
            } elseif ($date == null) {
                throw new Exception('Tanggal tidak boleh kosong');
            } elseif (empty(strlen(trim($description)))) {
                throw new Exception('Deskripsi tidak boleh kosong');
            }

            $point = $this->msachievementpoint->get(array(
                'id' => $achievementpointid
            ));

            if ($point->num_rows() == 0) {
                throw new Exception('Prestasi tidak ditemukan');
            }

            $row = $point->row();

            $insert = array();
            $insert['studentid'] = $studentid;
            $insert['achievementpointid'] = $achievementpointid;
            $insert['date'] = $date;
            $insert['description'] = $description;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $config = array();
            $config['allowed_types'] = 'jpg|jpeg|png|pdf';
            $config['upload_path'] = './uploads/achievement';
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            if ($document['size'] > 0) {
                if ($this->upload->do_upload('document')) {
                    $data = $this->upload->data();
                    $insert['document'] = $data['file_name'];
                    $insert['documenttype'] = $data['file_type'];
                } else {
                    throw new Exception('Gagal mengunggah dokumen');
                }
            }

            $saying = getTimeofnow();
            if ($rowstudent->parent_token != null) {
                sendNotificationToApps('Informasi Prestasi', $saying . ", Bapak/Ibu Wali Murid, kami ingin menginformasikan bahwa putra/putri Anda yang bernama *" . $rowstudent->name . "* telah mendapatkan prestasi dengan detail sebagai berikut: *" . $row->name . "* pada tanggal *" . tgl_indo($date) . "*", $rowstudent->parent_token);
                $insert['is_send'] = 1;
            }

            $message = "
*Halo, $saying.*
    
Dengan hormat, Bapak/Ibu Wali Murid,

Kami ingin menginformasikan bahwa putra/putri Anda yang bernama *" . $rowstudent->name . "* telah berhasil meraih prestasi berikut:

✨ *Detail Prestasi* ✨  
📌 *Poin Prestasi*: *" . $row->point . "*  
🏆 *Jenis Prestasi*: *" . $row->name . "*  
🗓️ *Waktu Prestasi*: *" . tgl_indo($date) . "*  
📝 *Keterangan*: *" . $description . "*

🎉 Kami mengucapkan selamat atas pencapaian ini! Semoga menjadi motivasi untuk terus berprestasi. 🎉

Terima kasih.
";
            $whatsapp = sendMessageWhatsapp($rowstudent->phonenumber, $message);

            if (isset($whatsapp->RESULT) && $whatsapp->RESULT == 'OK') {
                $insert['is_send'] = 1;
            }

            $this->tbachievement->insert($insert);

            $insert = array();
            $insert['studentid'] = $studentid;
            $insert['title'] = 'Informasi Prestasi';
            $insert['message'] = $saying . ", Bapak/Ibu Wali Murid, kami ingin menginformasikan bahwa putra/putri Anda yang bernama *" . $rowstudent->name . "* telah mendapatkan prestasi dengan detail sebagai berikut: *" . $row->name . "* pada tanggal *" . tgl_indo($date) . "*";
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = getCurrentIdUser();

            $this->tbnotifications->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal ditambahkan');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit_achievement($studentid, $id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cek = $this->tbachievement->select('a.*,b.id as studentid, b.name as studentname, c.level,c.name as classname, d.name as teachername,e.point')
            ->join('msstudent b', 'b.id = a.studentid')
            ->join('msclass c', 'c.id = b.classid')
            ->join('msteacher d', 'd.id = c.teacherid', 'left')
            ->join('msachievementpoint e', 'e.id = a.achievementpointid')
            ->get(array(
                'a.id' => $id,
                'a.studentid' => $studentid
            ));

        if ($cek->num_rows() == 0) {
            return redirect(base_url('counseling/achievement'));
        }

        $data = array();
        $data['title'] = 'Bimbingan Konseling - Ubah Prestasi Peserta Didik';
        $data['content'] = 'admin/counseling/achievement/edit';
        $data['achievement'] = $cek->row();
        $data['achievementpoint'] = $this->msachievementpoint->order_by('name', 'ASC')->get(array(
            'createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()
        ))->result();

        return $this->load->view('master', $data);
    }

    public function process_edit_achievement($studentid, $id)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $cek = $this->tbachievement->get(array(
                'id' => $id,
                'studentid' => $studentid
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $cek->row();

            $achievement = getPost('achievement');
            $date = getPost('date');
            $description = getPost('description');
            $document = $_FILES['document'];

            if ($achievement == null) {
                throw new Exception('Prestasi tidak boleh kosong');
            } elseif ($date == null) {
                throw new Exception('Tanggal tidak boleh kosong');
            } elseif (empty(strlen(trim($description)))) {
                throw new Exception('Deskripsi tidak boleh kosong');
            }

            $cekstudent = $this->msstudent->get(array(
                'id' => $studentid,
                'isdeleted' => null,
                'status' => 'Active'
            ));

            if ($cekstudent->num_rows() == 0) {
                throw new Exception('Peserta didik tidak ditemukan');
            }

            $update = array();
            $update['studentid'] = $studentid;
            $update['achievementpointid'] = $achievement;
            $update['date'] = $date;
            $update['description'] = $description;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $config = array();
            $config['allowed_types'] = 'jpg|jpeg|png|pdf';
            $config['upload_path'] = './uploads/achievement';
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            if ($document['size'] > 0) {
                if ($this->upload->do_upload('document')) {
                    $data = $this->upload->data();
                    $update['document'] = $data['file_name'];
                    $update['documenttype'] = $data['file_type'];

                    if ($row->document != null && file_exists('./uploads/achievement/' . $row->document)) {
                        @unlink('./uploads/achievement/' . $row->document);
                    }
                } else {
                    throw new Exception('Gagal mengunggah dokumen');
                }
            }

            $this->tbachievement->update(array('id' => $id), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal diubah');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil diubah');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_delete_achievement($studentid)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $id = getPost('id');

            $get = $this->tbachievement->get(array(
                'id' => $id,
                'studentid' => $studentid
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $this->tbachievement->delete(array('id' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal dihapus');
            } else {
                if ($row->document != null && file_exists('./uploads/achievement/' . $row->document)) {
                    @unlink('./uploads/achievement/' . $row->document);
                }

                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil dihapus');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function getAchievement()
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $achievementid = getPost('achievementid');

        $get = $this->msachievementpoint->get(array(
            'id' => $achievementid
        ));

        return JSONResponse(array(
            'POINT' => $get->num_rows() > 0 ? $get->row()->point : '',
        ));
    }

    public function datatables_problem($studentid)
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('TbProblem', 'QueryDatatables', 'SearchDatatables');

        $where = array();

        // $academicyear = getCurrentAcademicyear();
        // $academicyearstart = $academicyear->startdate ?? null;
        // $academicyearend = $academicyear->enddate ?? null;

        // if ($academicyearstart != null) {
        //     $where['a.date >='] = $academicyearstart;
        // }

        // if ($academicyearend != null) {
        //     $where['a.date <='] = $academicyearend;
        // }

        $where['b.status'] = 'Active';
        $where['a.studentid'] = $studentid;
        $where['b.isdeleted'] = null;

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();

            $text = $value->studentname . " pada tanggal " . tgl_indo($value->date);

            $actions = "";
            if (isAdmin()) {
                $actions = "<div class=\"d-flex\">
                    <a href=\"" . base_url('counseling/data/detail/' . $studentid . '/problem/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2 mb-1\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                        <i class=\"ti ti-edit\"></i>
                    </a>

                    <button type=\"button\" class=\"btn btn-danger btn-sm ms-2 mb-1\" onclick=\"deleteProblem('" . $value->id . "','" . $text . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                        <i class=\"ti ti-trash\"></i>
                    </button>
                </div>";
            }

            if ($value->documenttype == 'application/pdf' && $value->document != null && file_exists('./uploads/problem/' . $value->document)) {
                $actions .= "<div class=\"d-flex\">
                    <a href=\"" . base_url('uploads/problem/' . $value->document) . "\" class=\"btn btn-danger btn-sm ms-2 mb-1\" target=\"_blank\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"PDF\">
                        <i class=\"ti ti-file-text\"></i>
                    </a>
                </div>";
            } elseif ($value->documenttype != 'application/pdf' && $value->document != null  && file_exists('./uploads/problem/' . $value->document)) {
                $actions .= "<div class=\"d-flex\">
                    <a href=\"" . base_url('uploads/problem/' . $value->document) . "\" class=\"btn btn-success btn-sm ms-2 mb-1\" target=\"_blank\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-success\" data-bs-original-title=\"Gambar\">
                        <i class=\"ti ti-photo\"></i>
                    </a>
                </div>";
            } else {
                if (!isAdmin()) {
                    $actions .= "N/A";
                }
            }

            $detail[] = tgl_indo($value->date);
            $detail[] = $value->studentname;
            $detail[] = ($value->level ?? null) != null ? $value->level . ' - ' . $value->classname : $value->classname;
            $detail[] = $value->problemname ?? '-';
            $detail[] = $value->problempoint ?? 0;
            $detail[] = $value->description;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function add_problem($studentid)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cekstudent = $this->msstudent->select('a.*, b.level,b.name as classname, c.name as teachername')
            ->join('msclass b', 'b.id = a.classid')
            ->join('msteacher c', 'c.id = b.teacherid', 'left')
            ->where(array(
                'a.id' => $studentid,
                'a.status' => 'Active',
                'a.isdeleted' => null
            ))
            ->get();

        if ($cekstudent->num_rows() == 0) {
            return redirect(base_url('counseling/data'));
        }

        $data = array();
        $data['title'] = 'Bimbingan Konseling - Tambah Permasalahan Peserta Didik';
        $data['content'] = 'admin/counseling/problem/add';
        $data['student'] = $cekstudent->row();
        $data['problempoint'] = $this->msproblempoint->order_by('name', 'ASC')->get(array('createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->result();

        return $this->load->view('master', $data);
    }

    public function process_add_problem($studentid)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $cekstudent = $this->msstudent->get(array(
                'id' => $studentid,
                'isdeleted' => null,
                'status' => 'Active'
            ));

            if ($cekstudent->num_rows() == 0) {
                throw new Exception('Peserta didik tidak ditemukan');
            }

            $rowstudent = $cekstudent->row();

            $problempointid = getPost('problem');
            $date = getPost('date');
            $description = getPost('description');
            $document = $_FILES['document'];

            if ($problempointid == null) {
                throw new Exception('Permasalahan tidak boleh kosong');
            } elseif ($date == null) {
                throw new Exception('Tanggal tidak boleh kosong');
            } elseif (empty(strlen(trim($description)))) {
                throw new Exception('Deskripsi tidak boleh kosong');
            }

            $cekproblempoint = $this->msproblempoint->get(array(
                'id' => $problempointid
            ));

            if ($cekproblempoint->num_rows() == 0) {
                throw new Exception('Permasalahan tidak ditemukan');
            }

            $rowproblempoint = $cekproblempoint->row();

            $insert = array();
            $insert['studentid'] = $studentid;
            $insert['problempointid'] = $problempointid;
            $insert['date'] = $date;
            $insert['description'] = $description;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $config = array();
            $config['allowed_types'] = 'jpg|jpeg|png|pdf';
            $config['upload_path'] = './uploads/problem';
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            if ($document['size'] > 0) {
                if ($this->upload->do_upload('document')) {
                    $data = $this->upload->data();
                    $insert['document'] = $data['file_name'];
                    $insert['documenttype'] = $data['file_type'];
                } else {
                    throw new Exception('Gagal mengunggah dokumen');
                }
            }

            $saying = getTimeofnow();
            if ($rowstudent->parent_token != null) {
                sendNotificationToApps('Informasi Permasalahan', $saying . ", Bapak/Ibu Wali Murid, kami ingin menginformasikan bahwa putra/putri Anda yang bernama *" . $rowstudent->name . "* memiliki permasalahan dengan detail sebagai berikut: *" . $rowproblempoint->name . "* pada tanggal *" . tgl_indo($date) . "*", $rowstudent->parent_token);
                $insert['is_send'] = 1;
            }

            $message = "
*Halo, $saying.*
    
Dengan hormat, Bapak/Ibu Wali Murid,

Kami ingin menginformasikan bahwa putra/putri Anda yang bernama *" . $rowstudent->name . "* memiliki permasalahan dengan detail sebagai berikut:

❗ *Permasalahan*: *" . $rowproblempoint->name . "*  
📌 *Poin Masalah*: *" . $rowproblempoint->point . "*  
🗓️ *Waktu Kejadian*: *" . tgl_indo($date) . "*  
📝 *Keterangan*: *" . $description . "*

Kami berharap kerja sama dari Bapak/Ibu untuk membantu menyelesaikan permasalahan ini.

Terima kasih atas perhatian Bapak/Ibu.
";

            $whatsapp = sendMessageWhatsapp($rowstudent->phonenumber, $message);

            if (isset($whatsapp->RESULT) && $whatsapp->RESULT == 'OK') {
                $insert['is_send'] = 1;
            }

            $this->tbproblem->insert($insert);

            $insert = array();
            $insert['studentid'] = $studentid;
            $insert['title'] = 'Informasi Permasalahan';
            $insert['message'] = $saying . ", Bapak/Ibu Wali Murid, kami ingin menginformasikan bahwa putra/putri Anda yang bernama *" . $rowstudent->name . "* memiliki permasalahan dengan detail sebagai berikut: *" . $rowproblempoint->name . "* pada tanggal *" . tgl_indo($date) . "*";
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = getCurrentIdUser();

            $this->tbnotifications->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal ditambahkan');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit_problem($studentid, $id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cek = $this->tbproblem->select('a.*,b.id as studentid, b.name as studentname, c.level,c.name as classname, d.name as teachername, e.point')
            ->join('msstudent b', 'b.id = a.studentid')
            ->join('msclass c', 'c.id = b.classid')
            ->join('msteacher d', 'd.id = c.teacherid', 'left')
            ->join('msproblempoint e', 'e.id = a.problempointid')
            ->where(array(
                'a.id' => $id,
                'a.studentid' => $studentid
            ))
            ->get();

        if ($cek->num_rows() == 0) {
            return redirect(base_url('counseling/problem'));
        }

        $data = array();
        $data['title'] = 'Bimbingan Konseling - Ubah Permasalahan Peserta Didik';
        $data['content'] = 'admin/counseling/problem/edit';
        $data['problem'] = $cek->row();
        $data['problempoint'] = $this->msproblempoint->order_by('name', 'ASC')->get(array('createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->result();

        return $this->load->view('master', $data);
    }

    public function process_edit_problem($studentid, $id)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $cek = $this->tbproblem->get(array(
                'id' => $id
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $cek->row();

            $cekstudent = $this->msstudent->get(array(
                'id' => $studentid,
                'isdeleted' => null,
                'status' => 'Active'
            ));

            if ($cekstudent->num_rows() == 0) {
                throw new Exception('Peserta didik tidak ditemukan');
            }

            $problempointid = getPost('problem');
            $date = getPost('date');
            $description = getPost('description');
            $document = $_FILES['document'];

            if ($problempointid == null) {
                throw new Exception('Permasalahan tidak boleh kosong');
            } elseif ($date == null) {
                throw new Exception('Tanggal tidak boleh kosong');
            } elseif (empty(strlen(trim($description)))) {
                throw new Exception('Deskripsi tidak boleh kosong');
            }

            $update = array();
            $update['studentid'] = $studentid;
            $update['problempointid'] = $problempointid;
            $update['date'] = $date;
            $update['description'] = $description;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $config = array();
            $config['allowed_types'] = 'jpg|jpeg|png|pdf';
            $config['upload_path'] = './uploads/problem';
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            if ($document['size'] > 0) {
                if ($this->upload->do_upload('document')) {
                    $data = $this->upload->data();
                    $update['document'] = $data['file_name'];
                    $update['documenttype'] = $data['file_type'];

                    if ($row->document != null && file_exists('./uploads/problem/' . $row->document)) {
                        @unlink('./uploads/problem/' . $row->document);
                    }
                } else {
                    throw new Exception('Gagal mengunggah dokumen');
                }
            }

            $this->tbproblem->update(array('id' => $id), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal diubah');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil diubah');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_delete_problem($studentid)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $id = getPost('id');

            $get = $this->tbproblem->get(array(
                'id' => $id,
                'studentid' => $studentid
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $this->tbproblem->delete(array('id' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal dihapus');
            } else {
                if ($row->document != null && file_exists('./uploads/problem/' . $row->document)) {
                    @unlink('./uploads/problem/' . $row->document);
                }

                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil dihapus');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function getProblem()
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $problemid = getPost('problemid');

        $get = $this->msproblempoint->get(array(
            'id' => $problemid
        ));

        return JSONResponse(array(
            'POINT' => $get->num_rows() > 0 ? $get->row()->point : '',
        ));
    }
}
